# clang-format configuration for DC Communication System
# Based on LLVM style with custom modifications

BasedOnStyle: LLVM

# 基本格式设置
IndentWidth: 4
UseTab: Never
TabWidth: 4
ColumnLimit: 100

# 大括号风格
BreakBeforeBraces: Linux
Cpp11BracedListStyle: true

# 空格设置
SpaceBeforeParens: ControlStatements
SpaceInEmptyParentheses: false
SpacesInParentheses: false
SpacesInSquareBrackets: false
SpaceAfterCStyleCast: true
SpaceBeforeAssignmentOperators: true
SpacesBeforeTrailingComments: 2

# 对齐设置
AlignAfterOpenBracket: Align
AlignConsecutiveAssignments: false
AlignConsecutiveDeclarations: false
AlignEscapedNewlines: Right
AlignOperands: true
AlignTrailingComments: true

# 换行设置
AllowShortBlocksOnASingleLine: false
AllowShortCaseLabelsOnASingleLine: false
AllowShortFunctionsOnASingleLine: None
AllowShortIfStatementsOnASingleLine: false
AllowShortLoopsOnASingleLine: false
AlwaysBreakAfterReturnType: None
AlwaysBreakBeforeMultilineStrings: false
AlwaysBreakTemplateDeclarations: false

# 函数参数
BinPackArguments: false
BinPackParameters: false
BreakBeforeBinaryOperators: None
BreakBeforeTernaryOperators: true
BreakConstructorInitializers: BeforeColon
BreakStringLiterals: true

# 注释格式
ReflowComments: true
CommentPragmas: '^ IWYU pragma:'

# 包含文件排序
SortIncludes: true
IncludeBlocks: Preserve

# 指针和引用对齐
PointerAlignment: Right
ReferenceAlignment: Right

# 其他设置
KeepEmptyLinesAtTheStartOfBlocks: false
MaxEmptyLinesToKeep: 2
NamespaceIndentation: None
PenaltyBreakAssignment: 2
PenaltyBreakBeforeFirstCallParameter: 19
PenaltyBreakComment: 300
PenaltyBreakFirstLessLess: 120
PenaltyBreakString: 1000
PenaltyExcessCharacter: 1000000
PenaltyReturnTypeOnItsOwnLine: 60

# C特定设置
Standard: C99
DisableFormat: false
ForEachMacros: ['FOREACH', 'Q_FOREACH', 'BOOST_FOREACH']
