
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <time.h>
#include <linux/fs.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/vfs.h>
#include <dirent.h>

#include "common.h"
#include "config.h"
#include "dcCommunication.h"
#include "recCommunication.h"
#include "filePropertiesTable.h"
#include "recFilePropertiesTable.h"
#include "fcFilePropertiesTable.h"
#include "bitHelper.h"
#include "netRecv.h"
#include "netReplay.h"
#include "fcFileListManage.h"
#include "deviceInfo.h"

static int g_dcCtrlDbgE = 1;



void dcCtrlDbgEn(int en)
{
	g_dcCtrlDbgE = en;
}

int getDcCtrlDbgStatus(void)
{
	return g_dcCtrlDbgE;
}

T_DcCommunicationPara g_DcCommunicationPara = {NULL, NULL, {{0},0,{NULL,NULL}}, -1, -1, 0, 0, 0, 0, NULL, 0, 0, 0, {0}, 0};

void dcCommunicationMutexLock(void)
{
    mutexLock(g_DcCommunicationPara.mutex, -1);
}

void dcCommunicationMutexUnlock(void)
{
    mutexUnlock(g_DcCommunicationPara.mutex);
}

T_DcCommunicationReqList* createDcCommunicationListNode(uint8_x* pBuf, uint32_x len)
{
    T_DcCommunicationReqList* pLsitNode = NULL;

    pLsitNode = (T_DcCommunicationReqList*)malloc(sizeof(T_DcCommunicationReqList));
    if(pLsitNode!=NULL)
    {
        memcpy(pLsitNode->pBuf, pBuf, len);
        pLsitNode->len = len;
    }
    else
    {
        CRP_APP_ERR("createDcCommunicationListNode failed! malloc failed!");
    }
    return pLsitNode;
}

int getFirstDcCommunicationListNode(T_DcCommunicationReqList** ppReqInfo)
{
    int ret;
    T_DcCommunicationPara* pDcCommunicationPara = &g_DcCommunicationPara;
    T_DcCommunicationReqList *pLsitNode;

    if(pDcCommunicationPara->runFlag!=1 || list_empty(&pDcCommunicationPara->list.listHead))
    {
//        CRP_APP_INFO("List is empty!");
        ret = -1;
    }
    else
    {
        dcCommunicationMutexLock();
        pLsitNode = list_first_entry(&pDcCommunicationPara->list.listHead, T_DcCommunicationReqList, listHead);
        *ppReqInfo = pLsitNode;
        list_del(&pLsitNode->listHead);
		pDcCommunicationPara->nodeNum--;
	    CRP_APP_DBG("pDcCommunicationPara->nodeNum--,pDcCommunicationPara->nodeNum value:%llu",pDcCommunicationPara->nodeNum);
        dcCommunicationMutexUnlock();
        ret = 0;
    }
    
    return ret;
}

void releaseDcCommunicationListNode(T_DcCommunicationReqList* pLsitNode)
{
    if(pLsitNode)
    {
        free(pLsitNode);
    }
}

void switchDcReqMsgHeadEndian(DC_REQ_FRAME_COMMON_HEAD *pCommonHead)
{
    pCommonHead->len        = SWAP16Ex(pCommonHead->len);
    pCommonHead->msPassed   = SWAP32Ex(pCommonHead->msPassed);
    pCommonHead->fcId       = SWAP32Ex(pCommonHead->fcId);
    pCommonHead->cmd        = SWAP16Ex(pCommonHead->cmd);
}

void switchDcRspMsgHeadEndian(DC_RSP_FRAME_COMMON_HEAD *pCommonHead)
{
    pCommonHead->len        = SWAP16Ex(pCommonHead->len);
    pCommonHead->reserved   = SWAP16Ex(pCommonHead->reserved);
    pCommonHead->msPassed   = SWAP32Ex(pCommonHead->msPassed);
    pCommonHead->fcId       = SWAP32Ex(pCommonHead->fcId);
    pCommonHead->cmd        = SWAP16Ex(pCommonHead->cmd);
    pCommonHead->exeState   = SWAP16Ex(pCommonHead->exeState);
}

void switchDcCommonReqBodyEndian(DC_COMMUNICATION_REQ *pCommonReq)
{
    int i;
    pCommonReq->deviceIdOfFile = SWAP32Ex(pCommonReq->deviceIdOfFile);
    for(i=0; i<DC_CHN_NUM; i++)
    {
        pCommonReq->did[i] = SWAP32Ex(pCommonReq->did[i]);
    }
}

void switchDcUploadReqBodyEndian(DC_UPLOAD_REQ *pUploadReq)
{
    int i;
    for(i=0; i<DC_MAX_UPLOAD_FILE_COUNT; i++)
    {
        pUploadReq->uploadChnInfo[i].destDeviceId = SWAP32Ex(pUploadReq->uploadChnInfo[i].destDeviceId);
    }
}

void switchDcDeleteReqBodyEndian(DC_DELETE_FILE_REQ *pDeleteReq)
{
    int i;
    for(i=0; i<DC_MAX_DELETE_FILE_COUNT; i++)
    {
        pDeleteReq->delCmdInfo[i].deviceIdOfFile = SWAP32Ex(pDeleteReq->delCmdInfo[i].deviceIdOfFile);
    }
}

void showDcReqMsgHead(DC_REQ_FRAME_COMMON_HEAD *pCommonHead)
{
    CRP_APP_INFO("sReqMsgHead:%#x %#x %#x %#x %#x %#x %#x len:%#x(%d) msPassed:%#x(%d) fcId:%#x cmd:%#x(%s)", 
                 pCommonHead->head[0], pCommonHead->head[1], pCommonHead->head[2], pCommonHead->head[3], 
                 pCommonHead->head[4], pCommonHead->head[5], pCommonHead->head[6], pCommonHead->len, 
                 pCommonHead->len, pCommonHead->msPassed, pCommonHead->msPassed, pCommonHead->fcId,
                 pCommonHead->cmd, getCmdDesc(pCommonHead->cmd));
}

void showDcRspMsgHead(DC_RSP_FRAME_COMMON_HEAD *pCommonHead)
{
    CRP_APP_INFO("sRspMsgHead:%#x %#x %#x %#x %#x %#x %#x len:%#x(%d) msPassed:%#x(%d) fcId:%#x cmd:%#x(%s) exeState:%#x", 
                 pCommonHead->head[0], pCommonHead->head[1], pCommonHead->head[2], pCommonHead->head[3], 
                 pCommonHead->head[4], pCommonHead->head[5], pCommonHead->head[6], pCommonHead->len, 
                 pCommonHead->len, pCommonHead->msPassed, pCommonHead->msPassed, pCommonHead->fcId,
                 pCommonHead->cmd, getCmdDesc(pCommonHead->cmd), pCommonHead->exeState);
}

void fillDcRspCommunicateFrame(DC_COMMUNICATION_RSP *pRsp, DC_REQ_FRAME_COMMON_HEAD *pReqCommonHead, int16_x exeState, uint8_x* extraInfo, uint32_x extraInfoLen)
{
    memset(pRsp, 0, sizeof(DC_COMMUNICATION_RSP));
    memset(pRsp->commonHead.head, DC_RSP_HEAD_FLAG, DC_HEAD_FLAG_LEN);
    pRsp->commonHead.len = 0;
    pRsp->commonHead.reserved = pReqCommonHead->cmd;
    pRsp->commonHead.msPassed = pReqCommonHead->msPassed;
    pRsp->commonHead.fcId = pReqCommonHead->fcId;
    pRsp->commonHead.cmd = pReqCommonHead->cmd;
    pRsp->commonHead.exeState = exeState;
    pRsp->checkSum = 0;
    pRsp->extraInfo = extraInfo;
    pRsp->extraInfoLen = extraInfoLen;
}

void fillDcCommunicateReportFrame(DC_COMMUNICATION_RSP *pRsp, uint16_x cmd, int16_x exeState, uint8_x* extraInfo, uint32_x extraInfoLen)
{
    memset(pRsp->commonHead.head, DC_RSP_HEAD_FLAG, DC_HEAD_FLAG_LEN);
    pRsp->commonHead.len = 0;
    pRsp->commonHead.reserved = cmd;
    pRsp->commonHead.msPassed = getPassedMsOfDay();
    pRsp->commonHead.fcId = FC_DEVICE_ID(getFcChassisId());
    pRsp->commonHead.cmd = cmd;
    pRsp->commonHead.exeState = exeState;
    pRsp->checkSum = 0;
    pRsp->extraInfo = extraInfo;
    pRsp->extraInfoLen = extraInfoLen;
}

/* 长度和校验和字段填写、回告帧拼接、非自定义部分大小端转换、组播发送 */
int sendDcCommunicateRsp(DC_COMMUNICATION_RSP *pRsp)
{
    T_DcCommunicationPara* pCommunicationPara = &g_DcCommunicationPara;
    char pTempBuf[DC_MAX_MSG_SIZE];
    int ret = -1;
    short sMsgLen = sizeof(DC_COMMUNICATION_RSP) - sizeof(uint8_x*) - sizeof(uint32_x) + pRsp->extraInfoLen;

    if(sMsgLen > DC_MAX_MSG_SIZE)
    {
        CRP_APP_ERR("illegal nDataLen:%d", sMsgLen);
        return -1;
    }   
    memset(pTempBuf, 0, sizeof(pTempBuf));
    pRsp->commonHead.len = sMsgLen - DC_HEAD_FLAG_LEN;
    memcpy(pTempBuf, pRsp, sizeof(DC_RSP_FRAME_COMMON_HEAD));
    switchDcRspMsgHeadEndian((DC_RSP_FRAME_COMMON_HEAD*)pTempBuf);
    if (NULL != pRsp->extraInfo && pRsp->extraInfoLen > 0)
    {
        memcpy(&pTempBuf[sizeof(DC_RSP_FRAME_COMMON_HEAD)], pRsp->extraInfo, pRsp->extraInfoLen);
    }
    *((uint8_x*)&pTempBuf[sizeof(DC_RSP_FRAME_COMMON_HEAD)+pRsp->extraInfoLen]) = calcCheckSum((uint8_x*)&pTempBuf[DC_HEAD_FLAG_LEN], sMsgLen-DC_HEAD_FLAG_LEN-1); 
    if(pCommunicationPara->runFlag && pCommunicationPara->rspSocket>0)
    {
        mutexLock(pCommunicationPara->rspMutex, -1);
        ret = multicastSend(pCommunicationPara->rspSocket, DC_COMMUNICATION_RSP_IP, DC_COMMUNICATION_RSP_PORT, (int8_x *)pTempBuf, sMsgLen);
        mutexUnlock(pCommunicationPara->rspMutex);
    }
    else
    {
        if(pCommunicationPara->runFlag)
        {
            CRP_APP_ERR("proc exit");
        }
        if(pCommunicationPara->rspSocket < 0)
        {
            CRP_APP_ERR("rspSocket exit");
        }
    }
    return (ret == sMsgLen) ? 0 : -2;
}

#if 0
/* dcChn通道号,1-16,分别对应存储模块1 1-4 9-12 存储模块2 1-4 9-12 */ 
int dcChn2BofeiChn(int dcChn, unsigned int nRecordDeviceTag, int bofeiChn)
{
    nRecordDeviceTag = (dcChn>8)?1:0;
    bofeiChn = ()
}
#endif

int getBofeiChnMask(unsigned int nRecordDeviceTag, DC_COMMUNICATION_REQ *pReq, int *pChnMask)
{
    int i;

    if(nRecordDeviceTag >= MAX_REC_DEVICE_TYPE_NUM)
    {
        CRP_APP_ERR("invalid nRecordDeviceTag %d, should be less than %d", nRecordDeviceTag, MAX_REC_DEVICE_TYPE_NUM);
        return -1;
    }
    if(pReq == NULL)
    {
        return -2;
    }
    *pChnMask = 0;
    /* dcChn通道号,0-15,分别对应存储模块1 0-3 8-11 存储模块2 0-3 8-11 */ 
    if(nRecordDeviceTag == 0)
    {
        for(i=0; i<4; i++)
        {
            if(pReq->did[i])
            {
                *pChnMask |= (1<<i);
            }
        }
        for(i=4; i<8; i++)
        {
            if(pReq->did[i])
            {
                *pChnMask |= (1<<(i+4));
            }
        }
    }
    else
    {
        for(i=8; i<12; i++)
        {
            if(pReq->did[i])
            {
                *pChnMask |= (1<<(i-8));
            }
        }
        for(i=12; i<DC_CHN_NUM; i++)
        {
            if(pReq->did[i])
            {
                *pChnMask |= (1<<(i-4));
            }
        }
    }
    return 0;
}

/* dcChn通道号,1-16,分别对应存储模块1 1-4 9-12 存储模块2 1-4 9-12 */ 
uint16_x getDcChn(unsigned int nRecordDeviceTag, uint16_x nBofeiChn)
{
    int dcChn;
    if(nBofeiChn>=1 && nBofeiChn<=4)
    {
        dcChn = (nRecordDeviceTag*8)+nBofeiChn;
    }
    else if(nBofeiChn>=9 && nBofeiChn<=12)
    {
        dcChn = (nRecordDeviceTag*8)+nBofeiChn-4;
    }
    else
    {
        dcChn = 0;
    }
    return dcChn;
}

unsigned long long convertBcd2TimeStampMs(DC_BCD_TIME_DEF* pBcdTimeDef)
{
    struct tm tm1;
    tm1.tm_year  = getBcdValue(pBcdTimeDef->yearH)*100 + getBcdValue(pBcdTimeDef->yearL) - 1900;
    tm1.tm_mon   = getBcdValue(pBcdTimeDef->month) - 1;
    tm1.tm_mday  = getBcdValue(pBcdTimeDef->day);
    tm1.tm_hour  = getBcdValue(pBcdTimeDef->hour);
    tm1.tm_min   = getBcdValue(pBcdTimeDef->minute);
    tm1.tm_sec   = getBcdValue(pBcdTimeDef->second);
    return (unsigned long long)mktime(&tm1)*1000 + getBcdValue(pBcdTimeDef->milSecondH)*100 + getBcdValue(pBcdTimeDef->milSecondL);
}

#if 0
T_BofeiRecordParamListInfo g_BofeiRecordParamListInfo;

int bofeiRecordParamListInit(void)
{
    T_BofeiRecordParamListInfo *pBofeiRecordParamListInfo = &g_BofeiRecordParamListInfo;
    
    pBofeiRecordParamListInfo->listMutex = mutexCreate();
    if(pBofeiRecordParamListInfo->listMutex == NULL)
    {
        CRP_APP_ERR("create bofeiRecordParamListMutex failed!");
        return -1;
    }
    memset(&pBofeiRecordParamListInfo->listNode, 0, sizeof(pBofeiRecordParamListInfo->listNode));
    INIT_LIST_HEAD(&pBofeiRecordParamListInfo->listNode.listHead);
}

void bofeiRecordParamListMutexLock(void)
{
    T_BofeiRecordParamListInfo *pBofeiRecordParamListInfo = &g_BofeiRecordParamListInfo;
    mutexLock(pBofeiRecordParamListInfo->listMutex,-1);
}

void bofeiRecordParamListMutexUnlock(void)
{
    T_BofeiRecordParamListInfo *pBofeiRecordParamListInfo = &g_BofeiRecordParamListInfo;
    mutexUnlock(pBofeiRecordParamListInfo->listMutex);
}

int addBofeiRecordParamListNode(T_BofeiRecordParam *pBofeiRecordParam)
{
    T_BofeiRecordParamNode pLsitNode = NULL;
    T_BofeiRecordParamListInfo *pBofeiRecordParamListInfo = &g_BofeiRecordParamListInfo;

    if(pBofeiRecordParam == NULL)
    {
        CRP_APP_ERR("invalid pBofeiRecordParam: NULL!");
        return -1;
    }
    pLsitNode = (T_BofeiRecordParamNode*)malloc(sizeof(T_BofeiRecordParamNode));
    if(pLsitNode!=NULL)
    {
        memcpy(pLsitNode->BofeiRecordParam, pBofeiRecordParam, sizeof(T_BofeiRecordParamNode));
        pLsitNode->stopChmask[0] = 0;
        pLsitNode->stopChmask[1] = 0;
        mutexLock(pBofeiRecordParamListInfo->listMutex,-1);
        list_add_tail(&pLsitNode->listHead, &pBofeiRecordParamListInfo->listNode.listHead);
        mutexUnlock(pBofeiRecordParamListInfo->listMutex);
        CRP_APP_INFO("************* debug addBofeiRecordParamListNode %p success!", pLsitNode);
        CRP_APP_INFO("coverEn: %d, recordMode: %d keyDataFlag:%d chnMask0:%#x chnMask1:%#x llStartTickMs:%llu llEndTickMs:%llu", 
                 pBofeiRecordParam->coverEn, pBofeiRecordParam->recordMode, pBofeiRecordParam->keyDataFlag, 
                 pBofeiRecordParam->chnMask[0], pBofeiRecordParam->chnMask[1], 
                 pBofeiRecordParam->llStartTickMs, pBofeiRecordParam->llEndTickMs);
        timeStampPrint(pBofeiRecordParam->llStartTickMs/1000);
        timeStampPrint(pBofeiRecordParam->llEndTickMs/1000);
    }
    else
    {
        CRP_APP_ERR("addBofeiRecordParamListNode failed! malloc failed!");
        return -2;
    }
    return 0;
}

T_BofeiRecordParamNode* getFirstBofeiRecordParamListNode(void)
{
    T_BofeiRecordParamNode pLsitNode = NULL;
    T_BofeiRecordParamListInfo *pBofeiRecordParamListInfo = &g_BofeiRecordParamListInfo;

    mutexLock(pBofeiRecordParamListInfo->listMutex,-1);
    if(!list_empty(&pBofeiRecordParamListInfo->listNode.listHead))
    {
        pLsitNode = list_first_entry(&pBofeiRecordParamListInfo->listNode.listHead, T_BofeiRecordParamNode, listHead);
    }
    mutexUnlock(pBofeiRecordParamListInfo->listMutex);

    return pLsitNode;
}

T_BofeiRecordParamNode* getLastBofeiRecordParamListNode(void)
{
    T_BofeiRecordParamNode pLsitNode = NULL;
    T_BofeiRecordParamListInfo *pBofeiRecordParamListInfo = &g_BofeiRecordParamListInfo;

    mutexLock(pBofeiRecordParamListInfo->listMutex,-1);
    if(!list_empty(&pBofeiRecordParamListInfo->listNode.listHead))
    {
        pLsitNode = list_last_entry(&pBofeiRecordParamListInfo->listNode.listHead, T_BofeiRecordParamNode, listHead);
    }
    mutexUnlock(pBofeiRecordParamListInfo->listMutex);

    return pLsitNode;
}
#endif
#if 0
T_BofeiRecordParamNode* findBofeiRecordParamListNodeByTimeStamp(int startTime)
{
    T_BofeiRecordParamListInfo *pBofeiRecordParamListInfo = &g_BofeiRecordParamListInfo;
    T_BofeiRecordParamNode *pLsitCurNode = NULL;
    T_BofeiRecordParamNode *pLsitNextNode = NULL;

    pBofeiRecordParamListInfo = &g_REC_FILE_LIST_INFO[nRecordDeviceTag];
    mutexLock(pBofeiRecordParamListInfo->listMutex, -1);
    list_for_each_entry_safe(pLsitCurNode, pLsitNextNode, &pBofeiRecordParamListInfo->listNode.listHead, listHead, T_BofeiRecordParamNode)
    {
        if(pLsitCurNode->head.nMsgTimeStamp == nTimeStamps)
        {
//            list_del(&pLsitCurNode->listHead);
            mutexUnlock(pBofeiRecordParamListInfo->listMutex);
            return pLsitCurNode;
        }
    }
    mutexUnlock(pBofeiRecordParamListInfo->listMutex);
    return NULL;
}
#endif

T_keyDataMsg g_keyDataMsg = {0, 0, 0}; /* 用来记录当前时刻柏飞记录文件是否属关键数据 */
T_BofeiRecordParam g_bofeiRecordParmPowerOn = {0}; /* 用于存储上电时刻柏飞记录参数配置,包括定时记录和上电记录 */
void *g_bofeiRecordParmPowerOnMutex = NULL;

int getKeyDataFlagState(void)
{
    return (g_keyDataMsg.g_keyDataFlag)?1:0;
}

void setKeyDataFlagState(int keyDataFlag)
{
    g_keyDataMsg.g_keyDataFlag = (keyDataFlag)?1:0;
}

void bofeiRecordParmPowerOnMutexLock(void)
{
    mutexLock(g_bofeiRecordParmPowerOnMutex, -1);
}

void bofeiRecordParmPowerOnMutexUnlock(void)
{
    mutexUnlock(g_bofeiRecordParmPowerOnMutex);
}

int initBofeiRecordParamPowerOn(void)
{
    T_BofeiRecordParam * pBofeiRecordParamPowerOn = &g_bofeiRecordParmPowerOn;

    g_bofeiRecordParmPowerOnMutex = mutexCreate();
    if(g_bofeiRecordParmPowerOnMutex == NULL)
    {
        CRP_APP_ERR("create g_bofeiRecordParmPowerOnMutex failed!");
        return -1;
    }
    bofeiRecordParmPowerOnMutexLock();
    memcpy(pBofeiRecordParamPowerOn, getBofeiRecordParam(), sizeof(T_BofeiRecordParam));
    if(pBofeiRecordParamPowerOn->recordMode == E_BOFEI_POWERON_RECORD)
    {
        setKeyDataFlagState((pBofeiRecordParamPowerOn->keyDataFlag)?1:0);
    }
    else
    {
        setKeyDataFlagState(0);
    }
    bofeiRecordParmPowerOnMutexUnlock();
    return 0;
}

T_BofeiRecordParam* getBofeiRecordParmPowerOn(void)
{
    return &g_bofeiRecordParmPowerOn;
}

int updateBofeiRecordParmPowerOn(T_BofeiRecordParam * pBofeiRecordParam)
{
    T_BofeiRecordParam * pBofeiRecordParamPowerOn = &g_bofeiRecordParmPowerOn;

    bofeiRecordParmPowerOnMutexLock();
    memcpy(pBofeiRecordParamPowerOn, pBofeiRecordParam, sizeof(T_BofeiRecordParam));
    bofeiRecordParmPowerOnMutexUnlock();
    return 0;
}

int dcStartBofeiRecord(int nRecordDeviceTag, T_BofeiRecordParam *pBofeiRecordParam)
{
    int ret = 0;
    if(pBofeiRecordParam->chnMask[nRecordDeviceTag])
    {
        recRecyleRecordControlReq(nRecordDeviceTag, (pBofeiRecordParam->coverEn)?1:0);  /* 柏飞记录板循环覆盖模式设置 */
        mdelay(100);
        ret = recRecordControlReq(nRecordDeviceTag, pBofeiRecordParam->chnMask[nRecordDeviceTag]); /* 柏飞记录板启动记录 */
        if(ret == 0)
        {
            setKeyDataFlagState((pBofeiRecordParam->keyDataFlag)?1:0);
            CRP_APP_INFO("setKeyDataFlagState %d", (pBofeiRecordParam->keyDataFlag)?1:0);
        }
    }
    else
    {
        ret = 0;
    }
    return ret;
}

int dcStopBofeiRecord(int nRecordDeviceTag, T_BofeiRecordParam *pBofeiRecordParam)
{
    return recRecordControlReq(nRecordDeviceTag, 0); /* 柏飞记录板停止记录 */
}

int dcStartBofeiReplay(DC_COMMUNICATION_REQ *pReq, unsigned short sCount)
{
    unsigned int nRecordDeviceTag;
    int ch,DcChn;
    int32_x chnMask[REC_MD_NUM];
    int ret[REC_MD_NUM];
    tagFileReplayChannleReqInfo FileReplayChannleReqInfo[MAX_REPLAY_CHANNEL_NUM];
    int busy[REC_MD_NUM] = {0,0};
    uint32_x workStatus;
    int map;
    
    for(nRecordDeviceTag=0; nRecordDeviceTag<REC_MD_NUM; nRecordDeviceTag++)
    {
        getBofeiChnMask(nRecordDeviceTag, pReq, &chnMask[nRecordDeviceTag]);
    }

    for(nRecordDeviceTag=0,map = 0;nRecordDeviceTag<REC_MD_NUM;nRecordDeviceTag++)
    {
        workStatus = getRecWorkStatus(nRecordDeviceTag);
        if(chnMask[nRecordDeviceTag] && getRecLiveStatus(nRecordDeviceTag) &&
           (workStatus == IDLE_STATUS || workStatus == DOWNLOAD_STATUS))
        {
            map |= (1<<nRecordDeviceTag);
        }
    }
    clearAutoDownloadIdleCountAll(map);
    
    for(nRecordDeviceTag=0; nRecordDeviceTag<REC_MD_NUM; nRecordDeviceTag++)
    {
        if(getRecLiveStatus(nRecordDeviceTag))
        {
            workStatus = getRecWorkStatus(nRecordDeviceTag);
            if(workStatus==IDLE_STATUS)
            {
                if(chnMask[nRecordDeviceTag])
                {
                    for(ch=0;ch<MAX_REPLAY_CHANNEL_NUM;ch++)
                    {
                        if((chnMask[nRecordDeviceTag]>>ch)&0x1)
                        {
                            FileReplayChannleReqInfo[ch].sCtrlCmd = RCF_START_REPLAY; /* 开始 */
                            FileReplayChannleReqInfo[ch].sCount = sCount;
                            DcChn = getDcChn(nRecordDeviceTag, ch+1);
                            strncpy(FileReplayChannleReqInfo[ch].strFilePath, (char*)pReq->didReplayFilePath[DcChn-1], sizeof(FileReplayChannleReqInfo[ch].strFilePath)-1);
                            FileReplayChannleReqInfo[ch].strFilePath[sizeof(FileReplayChannleReqInfo[ch].strFilePath)-1] = 0;
                        }
                        else
                        {
                            FileReplayChannleReqInfo[ch].sCtrlCmd = RCF_STOP_REPLAY; /* 停止 */
                            FileReplayChannleReqInfo[ch].sCount = 0;
                            memset(FileReplayChannleReqInfo[ch].strFilePath, 0, sizeof(FileReplayChannleReqInfo[ch].strFilePath));
                        }
                    }

                    ret[nRecordDeviceTag] = recReplayControlReq(nRecordDeviceTag, pReq->replayMode, pReq->replayDuty[nRecordDeviceTag], FileReplayChannleReqInfo);
                    if(ret[nRecordDeviceTag])
                    {
                        CRP_APP_ERR("nRecordDeviceTag:%d recReplayControlReq failed! replayMode:%d replayDuty:%d%% ret:%d", 
                                      nRecordDeviceTag, pReq->replayMode, pReq->replayDuty[nRecordDeviceTag], ret[nRecordDeviceTag]);
                    }
                }
                else
                {
                    ret[nRecordDeviceTag] = -1;
                } 
            }
            else
            {
                CRP_APP_ERR("nRecordDeviceTag:%d WorkStatus:%d, busy!", nRecordDeviceTag, workStatus);
                ret[nRecordDeviceTag] = -1;
                busy[nRecordDeviceTag] = 1;
            }
        }
        else
        {
            CRP_APP_ERR("nRecordDeviceTag:%d LiveStatus:%d!", nRecordDeviceTag, getRecLiveStatus(nRecordDeviceTag));
            ret[nRecordDeviceTag] = -1;
        }
    }
    return (ret[0]==0||ret[1]==0)?0:((busy[0]||busy[1])?DC_ERROR_NO_BUSY:-1);
}

int dcStopBofeiReplay(DC_COMMUNICATION_REQ *pReq)
{
    int nRecordDeviceTag;
    int ret[REC_MD_NUM];
    int busy[REC_MD_NUM] = {0,0};
    uint32_x workStatus;

    for(nRecordDeviceTag=0; nRecordDeviceTag<REC_MD_NUM; nRecordDeviceTag++)
    {
        if(getRecLiveStatus(nRecordDeviceTag))
        {
            workStatus = getRecWorkStatus(nRecordDeviceTag);
            if(workStatus==IDLE_STATUS || workStatus==REPLAY_STATUS)
            {
                ret[nRecordDeviceTag] = recReplayStopReq(nRecordDeviceTag);
                if(ret[nRecordDeviceTag])
                {
                    CRP_APP_ERR("nRecordDeviceTag:%d recReplayStopReq failed! ret:%d", ret[nRecordDeviceTag]);
                }
            }
            else if(workStatus!=INVALID_STATUS)
            {
                CRP_APP_ERR("nRecordDeviceTag:%d WorkStatus:%d, busy!", nRecordDeviceTag, workStatus);
                ret[nRecordDeviceTag] = -1;
                busy[nRecordDeviceTag] = 1;
            }
        }
        else
        {
            CRP_APP_ERR("nRecordDeviceTag:%d LiveStatus:%d!", nRecordDeviceTag, getRecLiveStatus(nRecordDeviceTag));
            ret[nRecordDeviceTag] = -1;
        }
    }
    
    return (ret[0]==0||ret[1]==0)?0:((busy[0]||busy[1])?DC_ERROR_NO_BUSY:-1);
}

int dcStartRecordReq(T_DcCommunicationReqList* pReqList)
{
    DC_COMMUNICATION_REQ *pReq;
    T_BofeiRecordParam BofeiRecordParam;
    uint8_x checkSum;
    int nRecordDeviceTag;
    int ret[REC_MD_NUM], ret1;
    DC_COMMUNICATION_RSP DcRsp;
    int busy[REC_MD_NUM] = {0,0};
    uint32_x workStatus;
    int map;
    
    pReq = (DC_COMMUNICATION_REQ*)pReqList->pBuf;
    switchDcCommonReqBodyEndian(pReq);
    checkSum = calcCheckSum((uint8_x*)&pReq->commonHead.len, sizeof(DC_COMMUNICATION_REQ)-DC_HEAD_FLAG_LEN-1); 
    if(checkSum != pReq->checkSum)
    {
        CRP_APP_ERR("cmd %d invalid checkSum:%#x, should be %#x", pReq->commonHead.cmd, checkSum, pReq->checkSum);
        ret1 = -1;
    }
    else
    {
        if(getDcCtrlDbgStatus())
        {
            CRP_APP_INFO("%s(%#x): workMode1: %d, workMode2: %d keyDataFlag:%d DID1:%d DID2:%d DID3:%d DID4:%d DID5:%d DID6:%d DID7:%d DID8:%d DID9:%d DID10:%d DID11:%d DID12:%d DID13:%d DID14:%d DID15:%d DID16:%d startTime:%04d-%02d-%02d %02d:%02d:%02d:%03d sendTime:%04d-%02d-%02d %02d:%02d:%02d:%03d", 
                         getCmdDesc(pReq->commonHead.cmd), pReq->commonHead.cmd, pReq->workMode1, pReq->workMode2, pReq->keyDataFlag, 
                         pReq->did[0], pReq->did[1], pReq->did[2], pReq->did[3], pReq->did[4], pReq->did[5], pReq->did[6], pReq->did[7],
                         pReq->did[8], pReq->did[9], pReq->did[10], pReq->did[11], pReq->did[12], pReq->did[13], pReq->did[14], pReq->did[15], 
                         getBcdValue(pReq->startTime.yearH)*100 + getBcdValue(pReq->startTime.yearL), getBcdValue(pReq->startTime.month), getBcdValue(pReq->startTime.day), 
                         getBcdValue(pReq->startTime.hour), getBcdValue(pReq->startTime.minute), getBcdValue(pReq->startTime.second), getBcdValue(pReq->startTime.milSecondH)*100 + getBcdValue(pReq->startTime.milSecondL),
                         getBcdValue(pReq->endTime.yearH)*100 + getBcdValue(pReq->endTime.yearL), getBcdValue(pReq->endTime.month), getBcdValue(pReq->endTime.day), 
                         getBcdValue(pReq->endTime.hour), getBcdValue(pReq->endTime.minute), getBcdValue(pReq->endTime.second), getBcdValue(pReq->endTime.milSecondH)*100 + getBcdValue(pReq->endTime.milSecondL));
        }
        BofeiRecordParam.coverEn = pReq->workMode1;
        BofeiRecordParam.recordMode = pReq->workMode2;
        BofeiRecordParam.keyDataFlag = (pReq->keyDataFlag)?1:0;
        getBofeiChnMask(0, pReq, &BofeiRecordParam.chnMask[0]);
        getBofeiChnMask(1, pReq, &BofeiRecordParam.chnMask[1]);
        BofeiRecordParam.llStartTickMs = convertBcd2TimeStampMs(&pReq->startTime);
        BofeiRecordParam.llEndTickMs = convertBcd2TimeStampMs(&pReq->endTime);
        BofeiRecordParam.status[0] = E_BOFEI_RECORD_TASK_IDLE;
        BofeiRecordParam.status[1] = E_BOFEI_RECORD_TASK_IDLE;

        switch(BofeiRecordParam.recordMode)
        {
            case E_BOFEI_POWERON_RECORD: /* 0-开机自动记录 */
                if(BofeiRecordParam.chnMask[0] == 0 && BofeiRecordParam.chnMask[1] == 0)
                {
                    CRP_APP_ERR("Error, chnMask1:%#x chnMask2:%#x!", BofeiRecordParam.chnMask[0], BofeiRecordParam.chnMask[1]);
                    ret1 = -1;
                }
                else
                {
                    ret1 = updateBofeiRecordParam(&BofeiRecordParam);
                }
                ret1 = (ret1)?-1:0;
                break;
            case E_BOFEI_RECORD_BY_CONRTOL: /* 1-即时开始 */
                updateBofeiRecordParam(&BofeiRecordParam);
                updateBofeiRecordParmPowerOn(&BofeiRecordParam);
                for(nRecordDeviceTag=0,map = 0;nRecordDeviceTag<REC_MD_NUM;nRecordDeviceTag++)
                {
                    workStatus = getRecWorkStatus(nRecordDeviceTag);
                    if(BofeiRecordParam.chnMask[nRecordDeviceTag] && getRecLiveStatus(nRecordDeviceTag) &&
                       (workStatus == IDLE_STATUS || workStatus == DOWNLOAD_STATUS))
                    {
                        map |= (1<<nRecordDeviceTag);
                    }
                }
                clearAutoDownloadIdleCountAll(map);
            
                for(nRecordDeviceTag=0; nRecordDeviceTag<REC_MD_NUM; nRecordDeviceTag++)
                {
                    if(BofeiRecordParam.chnMask[nRecordDeviceTag])
                    {
                        if(getRecLiveStatus(nRecordDeviceTag)) /* 柏飞记录板在线 */ 
                        {
                            workStatus = getRecWorkStatus(nRecordDeviceTag);
                            if(workStatus == IDLE_STATUS)
                            {
                                ret[nRecordDeviceTag] = dcStartBofeiRecord(nRecordDeviceTag, &BofeiRecordParam);
                            }
                            else
                            {
                                CRP_APP_ERR("nRecordDeviceTag:%d WorkStatus:%d, busy!", nRecordDeviceTag, workStatus);
                                ret[nRecordDeviceTag] = -1;
                                busy[nRecordDeviceTag] = 1;
                            }
                        }
                        else
                        { 
                            CRP_APP_ERR("nRecordDeviceTag:%d LiveStatus:%d!", nRecordDeviceTag, getRecLiveStatus(nRecordDeviceTag));
                            ret[nRecordDeviceTag] = -1;
                        }
                    }
                    else
                    {
                        ret[nRecordDeviceTag] = -1;
                    }
                }
                ret1 = (ret[0]==0||ret[1]==0)?0:((busy[0]||busy[1])?DC_ERROR_NO_BUSY:-1);
                break;
            case E_BOFEI_CONRTOL_BY_TIME: /* 2-指定时间段开始 */
                if(BofeiRecordParam.llStartTickMs > BofeiRecordParam.llEndTickMs)
                {
                    CRP_APP_ERR("Error, llStartTickMs:%llu > llEndTickMs:%llu!", BofeiRecordParam.llStartTickMs, BofeiRecordParam.llEndTickMs);
                    ret1 = -1;
                }
                else if(BofeiRecordParam.llEndTickMs <= get_cur_ms())
                {
                    CRP_APP_ERR("Error, llEndTickMs:%llu <= curTickMs:%llu!", BofeiRecordParam.llStartTickMs, get_cur_ms());
                    ret1 = -2;
                }
                else if(BofeiRecordParam.chnMask[0] == 0 && BofeiRecordParam.chnMask[1] == 0)
                {
                    CRP_APP_ERR("Error, chnMask1:%#x chnMask2:%#x!", BofeiRecordParam.chnMask[0], BofeiRecordParam.chnMask[1]);
                    ret1 = -3;
                }
                else
                {
                    ret1 = updateBofeiRecordParam(&BofeiRecordParam);
                    ret1 |= updateBofeiRecordParmPowerOn(&BofeiRecordParam);
                }
                ret1 = (ret1)?-1:0;
                break;
            default:
                CRP_APP_ERR("invalid workMode2 %d", BofeiRecordParam.recordMode);
                ret1 = -1; /* 暂不支持 */
                break;
        }
    }

    fillDcRspCommunicateFrame(&DcRsp, &pReq->commonHead, ret1, NULL, 0);
    sendDcCommunicateRsp(&DcRsp);
    return ret1;
}

int dcStopRecordReq(T_DcCommunicationReqList* pReqList)
{
    DC_COMMUNICATION_REQ *pReq;
    T_BofeiRecordParam BofeiRecordParam;
    uint8_x checkSum;
    uint32_x nRecordDeviceTag;
    int ret[REC_MD_NUM];
    DC_COMMUNICATION_RSP DcRsp;
    int busy[REC_MD_NUM] = {0,0};
    uint32_x workStatus;
    
    pReq = (DC_COMMUNICATION_REQ*)pReqList->pBuf;
    switchDcCommonReqBodyEndian(pReq);
    checkSum = calcCheckSum((uint8_x*)&pReq->commonHead.len, sizeof(DC_COMMUNICATION_REQ)-DC_HEAD_FLAG_LEN-1); 
    if(checkSum != pReq->checkSum)
    {
        CRP_APP_ERR("cmd %d invalid checkSum:%#x, should be %#x", pReq->commonHead.cmd, checkSum, pReq->checkSum);
        for(nRecordDeviceTag=0; nRecordDeviceTag<REC_MD_NUM; nRecordDeviceTag++)
        {
            ret[0] = -1;
        }
    }
    else
    {
        if(getDcCtrlDbgStatus())
        {
            CRP_APP_INFO("%s(%#x): workMode1: %d, workMode2: %d keyDataFlag:%d DID1:%d DID2:%d DID3:%d DID4:%d DID5:%d DID6:%d DID7:%d DID8:%d DID9:%d DID10:%d DID11:%d DID12:%d DID13:%d DID14:%d DID15:%d DID16:%d startTime:%04d-%02d-%02d %02d:%02d:%02d:%03d sendTime:%04d-%02d-%02d %02d:%02d:%02d:%03d", 
                         getCmdDesc(pReq->commonHead.cmd), pReq->commonHead.cmd, pReq->workMode1, pReq->workMode2, pReq->keyDataFlag, 
                         pReq->did[0], pReq->did[1], pReq->did[2], pReq->did[3], pReq->did[4], pReq->did[5], pReq->did[6], pReq->did[7],
                         pReq->did[8], pReq->did[9], pReq->did[10], pReq->did[11], pReq->did[12], pReq->did[13], pReq->did[14], pReq->did[15], 
                         getBcdValue(pReq->startTime.yearH)*100 + getBcdValue(pReq->startTime.yearL), getBcdValue(pReq->startTime.month), getBcdValue(pReq->startTime.day), 
                         getBcdValue(pReq->startTime.hour), getBcdValue(pReq->startTime.minute), getBcdValue(pReq->startTime.second), getBcdValue(pReq->startTime.milSecondH)*100 + getBcdValue(pReq->startTime.milSecondL),
                         getBcdValue(pReq->endTime.yearH)*100 + getBcdValue(pReq->endTime.yearL), getBcdValue(pReq->endTime.month), getBcdValue(pReq->endTime.day), 
                         getBcdValue(pReq->endTime.hour), getBcdValue(pReq->endTime.minute), getBcdValue(pReq->endTime.second), getBcdValue(pReq->endTime.milSecondH)*100 + getBcdValue(pReq->endTime.milSecondL));
        }
        BofeiRecordParam.coverEn = pReq->workMode1;
        BofeiRecordParam.recordMode = pReq->workMode2;
        BofeiRecordParam.keyDataFlag = (pReq->keyDataFlag)?1:0;
        getBofeiChnMask(0, pReq, &BofeiRecordParam.chnMask[0]);
        getBofeiChnMask(1, pReq, &BofeiRecordParam.chnMask[1]);
        BofeiRecordParam.llStartTickMs = convertBcd2TimeStampMs(&pReq->startTime);
        BofeiRecordParam.llEndTickMs = convertBcd2TimeStampMs(&pReq->endTime);

        for(nRecordDeviceTag=0; nRecordDeviceTag<REC_MD_NUM; nRecordDeviceTag++)
        {
            if(getRecLiveStatus(nRecordDeviceTag)) /* 柏飞记录板在线 */ 
            {
                workStatus = getRecWorkStatus(nRecordDeviceTag);
                if(workStatus == IDLE_STATUS || workStatus == RECORD_STATUS)
                {
                    ret[nRecordDeviceTag] = dcStopBofeiRecord(nRecordDeviceTag, &BofeiRecordParam);
                }
                else
                {
                    CRP_APP_ERR("nRecordDeviceTag:%d WorkStatus:%d, busy!", nRecordDeviceTag, workStatus);
                    ret[nRecordDeviceTag] = -1;
                    busy[nRecordDeviceTag] = 1;
                }
            }
            else
            { 
                CRP_APP_ERR("nRecordDeviceTag:%d LiveStatus:%d!", nRecordDeviceTag, getRecLiveStatus(nRecordDeviceTag));
                ret[nRecordDeviceTag] = -1;
            }
        }
    }
    
    fillDcRspCommunicateFrame(&DcRsp, &pReq->commonHead, (ret[0]==0||ret[1]==0)?0:((busy[0]||busy[1])?DC_ERROR_NO_BUSY:-1), NULL, 0);
    sendDcCommunicateRsp(&DcRsp);
    return (ret[0]|ret[1]);
}

int dcStartStopReplayReq(T_DcCommunicationReqList* pReqList)
{
    DC_COMMUNICATION_REQ *pReq;
    uint8_x checkSum;
    int ret;
    DC_COMMUNICATION_RSP DcRsp;
    
    pReq = (DC_COMMUNICATION_REQ*)pReqList->pBuf;
    switchDcCommonReqBodyEndian(pReq);
    checkSum = calcCheckSum((uint8_x*)&pReq->commonHead.len, sizeof(DC_COMMUNICATION_REQ)-DC_HEAD_FLAG_LEN-1); 
    if(checkSum != pReq->checkSum)
    {
        CRP_APP_ERR("cmd %d invalid checkSum:%#x, should be %#x", pReq->commonHead.cmd, checkSum, pReq->checkSum);
        ret = -1;
    }
    else
    {
        if(getDcCtrlDbgStatus())
        {
            CRP_APP_INFO("%s(%#x): replayMode:%d replayDuty1:%d replayDuty2:%d DID1:%d %s DID2:%d %s DID3:%d %s DID4:%d %s DID5:%d %s DID6:%d %s DID7:%d %s DID8:%d %s DID9:%d %s DID10:%d %s DID11:%d %s DID12:%d %s DID13:%d %s DID14:%d %s DID15:%d %s DID16:%d %s", 
                         getCmdDesc(pReq->commonHead.cmd), pReq->commonHead.cmd, pReq->replayMode, pReq->replayDuty[0], pReq->replayDuty[1], 
                         pReq->did[0], pReq->didReplayFilePath[0], pReq->did[1], pReq->didReplayFilePath[1], pReq->did[2], pReq->didReplayFilePath[2], 
                         pReq->did[3], pReq->didReplayFilePath[3], pReq->did[4], pReq->didReplayFilePath[4], pReq->did[5], pReq->didReplayFilePath[5], 
                         pReq->did[6], pReq->didReplayFilePath[6], pReq->did[7], pReq->didReplayFilePath[7],
                         pReq->did[8], pReq->didReplayFilePath[8], pReq->did[9], pReq->didReplayFilePath[9], pReq->did[10], pReq->didReplayFilePath[10], 
                         pReq->did[11], pReq->didReplayFilePath[11], pReq->did[12], pReq->didReplayFilePath[12], pReq->did[13], pReq->didReplayFilePath[13], 
                         pReq->did[14], pReq->didReplayFilePath[14], pReq->did[15], pReq->didReplayFilePath[15]);
        }
        if(pReq->commonHead.cmd == DC_CMD_RECYLE_REPLAY) /* 开始循环回放 */
        {
            ret = dcStartBofeiReplay(pReq, 0xFFFF);
        }
        else if(pReq->commonHead.cmd == DC_CMD_START_REPLAY) /* 开始回放 */
        {
            ret = dcStartBofeiReplay(pReq, 1);
        }
        else if(pReq->commonHead.cmd == DC_CMD_STOP_REPLAY) /* 停止回放 */
        {
            ret = dcStopBofeiReplay(pReq);
        }
        else
        {
            ret = -1;
        }
    }
    
    fillDcRspCommunicateFrame(&DcRsp, &pReq->commonHead, (ret==0)?0:-1, NULL, 0);
    sendDcCommunicateRsp(&DcRsp);
    return ret;
}

int dcGetFileListReq(T_DcCommunicationReqList* pReqList)
{
    DC_COMMUNICATION_REQ *pReq;
    uint8_x checkSum = 0;
    int ret = 0;
    uint32_x nRecordDeviceTag;
    T_file_list_info *pRecFileListInfo;
    tagRecFileList *pRecLsitCurNode = NULL;
    tagRecFileList *pRecLsitNextNode = NULL;
    T_fc_file_list_info *pfcFileListInfo;
    tagFcFileList *pFcLsitCurNode = NULL;
    tagFcFileList *pFcLsitNextNode = NULL;
    int firstFlag, lastFlag;
    DC_FILE_LIST_RSP_PACKET dcFileListRspPacket;
    uint16_x sFileTotalNum[REC_MD_NUM];
    uint16_x sFileTotalNumAll = 0;
    uint16_x sFileIndex, sPacketIndex;
    uint64_x llStartTickMs, llEndTickMs, llFileTimeMs;
    T_RecFilePropertiesInfo *pRecFilePropertiesInfo;
    T_FcFilePropertiesInfo *pFcFilePropertiesInfo;
    DC_COMMUNICATION_RSP DcRsp;
    uint16_x chn;
    
    pReq = (DC_COMMUNICATION_REQ*)pReqList->pBuf;
    switchDcCommonReqBodyEndian(pReq);
    checkSum = calcCheckSum((uint8_x*)&pReq->commonHead.len, sizeof(DC_COMMUNICATION_REQ)-DC_HEAD_FLAG_LEN-1); 
    if(checkSum != pReq->checkSum)
    {
        CRP_APP_ERR("cmd %d invalid checkSum:%#x, should be %#x", pReq->commonHead.cmd, checkSum, pReq->checkSum);
        ret = -1;
    }
    else
    {
        if(getDcCtrlDbgStatus())
        {
            CRP_APP_INFO("%s(%#x): workMode1: %d DID1:%d DID2:%d DID3:%d DID4:%d DID5:%d DID6:%d DID7:%d DID8:%d DID9:%d DID10:%d DID11:%d DID12:%d DID13:%d DID14:%d DID15:%d DID16:%d startTime:%04d-%02d-%02d %02d:%02d:%02d:%03d sendTime:%04d-%02d-%02d %02d:%02d:%02d:%03d", 
                         getCmdDesc(pReq->commonHead.cmd), pReq->commonHead.cmd, pReq->workMode1, 
                         pReq->did[0], pReq->did[1], pReq->did[2], pReq->did[3], pReq->did[4], pReq->did[5], pReq->did[6], pReq->did[7], 
                         pReq->did[8], pReq->did[9], pReq->did[10], pReq->did[11], pReq->did[12], pReq->did[13], pReq->did[14], pReq->did[15], 
                         getBcdValue(pReq->startTime.yearH)*100 + getBcdValue(pReq->startTime.yearL), getBcdValue(pReq->startTime.month), getBcdValue(pReq->startTime.day), 
                         getBcdValue(pReq->startTime.hour), getBcdValue(pReq->startTime.minute), getBcdValue(pReq->startTime.second), getBcdValue(pReq->startTime.milSecondH)*100 + getBcdValue(pReq->startTime.milSecondL),
                         getBcdValue(pReq->endTime.yearH)*100 + getBcdValue(pReq->endTime.yearL), getBcdValue(pReq->endTime.month), getBcdValue(pReq->endTime.day), 
                         getBcdValue(pReq->endTime.hour), getBcdValue(pReq->endTime.minute), getBcdValue(pReq->endTime.second), getBcdValue(pReq->endTime.milSecondH)*100 + getBcdValue(pReq->endTime.milSecondL));
        }
        llStartTickMs = convertBcd2TimeStampMs(&pReq->startTime);
        llEndTickMs = convertBcd2TimeStampMs(&pReq->endTime);
        if(llStartTickMs > llEndTickMs)
        {
            CRP_APP_ERR("Error, llStartTickMs:%llu > llEndTickMs:%llu!", llStartTickMs, llEndTickMs);
            ret = -1;
        }
        else
        {
            if(pReq->workMode1 == 0) /* 查询固态文件目录 */
            {
                recFileListMutexLock(0);
                recFileListMutexLock(1);
                sFileTotalNum[0] = getRecFileNumInRangeOfTime(0, llStartTickMs, llEndTickMs, pReq->did);
                sFileTotalNum[1] = getRecFileNumInRangeOfTime(1, llStartTickMs, llEndTickMs, pReq->did);
                sFileTotalNumAll = sFileTotalNum[0] + sFileTotalNum[1];
                if(getDcCtrlDbgStatus())
                {
                    CRP_APP_INFO("sFileTotalNum[0]:%d sFileTotalNum[1]:%d sFileTotalNum:%d", sFileTotalNum[0], sFileTotalNum[1], sFileTotalNumAll);
                }
                dcFileListRspPacket.totalFileNum = SWAP16Ex(sFileTotalNumAll);
                for(nRecordDeviceTag = 0, sFileIndex = 0, firstFlag = 1, lastFlag = 0; nRecordDeviceTag < REC_MD_NUM; nRecordDeviceTag++)
                {
                    pRecFileListInfo = getRecFileListInfo(nRecordDeviceTag);                    
                    list_for_each_entry_safe(pRecLsitCurNode, pRecLsitNextNode, &pRecFileListInfo->fileList.listHead, listHead, tagRecFileList)
                    {
                        pRecFilePropertiesInfo = &pRecLsitCurNode->filePropertiesInfo;
                        llFileTimeMs = pRecFilePropertiesInfo->FileDetailInfoByTime.llFileTime * 1000;
                        chn = pRecFilePropertiesInfo->FileDetailInfoByTime.nChannel;
                        if(((nRecordDeviceTag == 0 && chn >= 1 && chn <= 8 && pReq->did[chn-1]==1)||
                           (nRecordDeviceTag == 1 && chn >= 9 && chn <= 16 && pReq->did[chn-1]==1)) &&
                           (llFileTimeMs >= llStartTickMs && llFileTimeMs <=llEndTickMs))
                        {
                            sPacketIndex = sFileIndex%DC_PACKET_MAX_FILE_NUM;
                            dcFileListRspPacket.dcFileInfo[sPacketIndex].timeCreate = SWAP64Ex(llFileTimeMs);
                            dcFileListRspPacket.dcFileInfo[sPacketIndex].fileSize = SWAP64Ex(pRecFilePropertiesInfo->FileDetailInfoByTime.llFileSize);
                            dcFileListRspPacket.dcFileInfo[sPacketIndex].fileAttrib = SWAP32Ex((pRecFilePropertiesInfo->FileDetailInfoByTime.nFileType==0)?1:0);
                            strncpy(dcFileListRspPacket.dcFileInfo[sPacketIndex].pathname, pRecFilePropertiesInfo->FileDetailInfoByTime.strFilePath, sizeof(dcFileListRspPacket.dcFileInfo[sPacketIndex].pathname)-1);
                            dcFileListRspPacket.dcFileInfo[sPacketIndex].pathname[sizeof(dcFileListRspPacket.dcFileInfo[sPacketIndex].pathname)-1] = 0;
                            dcFileListRspPacket.dcFileInfo[sPacketIndex].deviceIdOfFile = SWAP32Ex(nRecordDeviceTag+1);
                            dcFileListRspPacket.dcFileInfo[sPacketIndex].did = SWAP16Ex(pRecFilePropertiesInfo->FileDetailInfoByTime.nChannel);
                            dcFileListRspPacket.dcFileInfo[sPacketIndex].keyDataFlag = SWAP16Ex((uint16_x)((pRecFilePropertiesInfo->keyDataFlag)?1:0));
                            dcFileListRspPacket.dcFileInfo[sPacketIndex].downloadedFlag = SWAP16Ex((uint16_x)pRecFilePropertiesInfo->downloadedFlag);
                            dcFileListRspPacket.dcFileInfo[sPacketIndex].osFileSize = SWAP64Ex(pRecFilePropertiesInfo->FileDetailInfoByTime.llOsFileSize);
                            if(getDcCtrlDbgStatus())
                            {
                                CRP_APP_INFO("sFileIndex:%03d sPacketIndex:%02d %64s %lldB %lldB(OS) deviceId:%d ch:%d keyDataFlag:%d downloadedFlag:%d", sFileIndex, sPacketIndex, 
                                              dcFileListRspPacket.dcFileInfo[sPacketIndex].pathname,
                                              pRecFilePropertiesInfo->FileDetailInfoByTime.llFileSize, pRecFilePropertiesInfo->FileDetailInfoByTime.llOsFileSize, nRecordDeviceTag+1,
                                              pRecFilePropertiesInfo->FileDetailInfoByTime.nChannel, pRecFilePropertiesInfo->keyDataFlag,
                                              pRecFilePropertiesInfo->downloadedFlag);
                            }
                            if((sPacketIndex == DC_PACKET_MAX_FILE_NUM-1) || (sFileIndex+1 == sFileTotalNumAll))
                            {
                                if(sFileIndex+1 == sFileTotalNumAll) /* 当前为最后一个文件 */
                                {
                                    dcFileListRspPacket.packetFlag = SWAP16Ex((uint16_x)DC_FILELIST_PACKET_END); /* 3：结束包 */
                                    lastFlag = 1;
                                }
                                else /* 当前不是最后一个文件 */
                                {
                                    if(firstFlag)
                                    {
                                        dcFileListRspPacket.packetFlag = SWAP16Ex((uint16_x)DC_FILELIST_PACKET_START); /* 1：开始包 */
                                        firstFlag = 0;
                                    }
                                    else
                                    {
                                        dcFileListRspPacket.packetFlag = SWAP16Ex((uint16_x)DC_FILELIST_PACKET_MIDDLE); /* 2：中间包 */
                                    }
                                }
                                if(getDcCtrlDbgStatus())
                                {
                                    CRP_APP_INFO("send packetFlag:%d fileNum:%02d",SWAP16Ex(dcFileListRspPacket.packetFlag),sPacketIndex+1);
                                }
                                fillDcRspCommunicateFrame(&DcRsp, &pReq->commonHead, 0, (uint8_x*)&dcFileListRspPacket, sizeof(uint16_x)+sizeof(uint16_x)+sizeof(DC_FILE_INFO)*(sPacketIndex+1));
                                sendDcCommunicateRsp(&DcRsp);
                                if(lastFlag)
                                {
                                    break;
                                }
                            }
                            sFileIndex++;
                        }
                    }    
                }
                recFileListMutexUnlock(0);
                recFileListMutexUnlock(1);
            }
            else /* 查询FC文件目录 */
            {
                pfcFileListInfo = getFcFileListInfo();
                fcFileListMutexLock();
                sFileTotalNumAll = getFcFileNumInRangeOfTime(llStartTickMs, llEndTickMs, pReq->did);
                if(getDcCtrlDbgStatus())
                {
                    CRP_APP_INFO("sFileTotalNum:%d", sFileTotalNumAll);
                }
                dcFileListRspPacket.totalFileNum = SWAP16Ex(sFileTotalNumAll);
                sFileIndex = 0;
                firstFlag = 1;
                lastFlag = 0;
                list_for_each_entry_safe(pFcLsitCurNode, pFcLsitNextNode, &pfcFileListInfo->fileList.listHead, listHead, tagFcFileList)
                {
                    pFcFilePropertiesInfo = &pFcLsitCurNode->filePropertiesInfo;
                    llFileTimeMs = pFcFilePropertiesInfo->FileDetailInfoByTime.llFileTime * 1000;
                    chn = pFcFilePropertiesInfo->FileDetailInfoByTime.nChannel;
                    if(chn < 1 || chn > 16 || pReq->did[chn-1]==0)
                    {
                        continue;
                    }
                    if(llFileTimeMs >= llStartTickMs && llFileTimeMs <=llEndTickMs)
                    {
                        sPacketIndex = sFileIndex%DC_PACKET_MAX_FILE_NUM;
                        dcFileListRspPacket.dcFileInfo[sPacketIndex].timeCreate = SWAP64Ex(llFileTimeMs);
                        dcFileListRspPacket.dcFileInfo[sPacketIndex].fileSize = SWAP64Ex(pFcFilePropertiesInfo->FileDetailInfoByTime.llFileSize);
                        dcFileListRspPacket.dcFileInfo[sPacketIndex].fileAttrib = SWAP32Ex((pFcFilePropertiesInfo->FileDetailInfoByTime.nFileType==0)?1:0);
                        strncpy(dcFileListRspPacket.dcFileInfo[sPacketIndex].pathname, pFcFilePropertiesInfo->FileDetailInfoByTime.strFilePath, sizeof(dcFileListRspPacket.dcFileInfo[sPacketIndex].pathname)-1);
                        dcFileListRspPacket.dcFileInfo[sPacketIndex].pathname[sizeof(dcFileListRspPacket.dcFileInfo[sPacketIndex].pathname)-1] = 0;
                        dcFileListRspPacket.dcFileInfo[sPacketIndex].deviceIdOfFile = SWAP32Ex(FC_DEVICE_ID(getFcChassisId()));
                        dcFileListRspPacket.dcFileInfo[sPacketIndex].did = SWAP16Ex(pFcFilePropertiesInfo->FileDetailInfoByTime.nChannel);
                        dcFileListRspPacket.dcFileInfo[sPacketIndex].keyDataFlag = SWAP16Ex((uint16_x)((pFcFilePropertiesInfo->keyDataFlag)?1:0));
                        dcFileListRspPacket.dcFileInfo[sPacketIndex].downloadedFlag = SWAP16Ex((uint16_x)pFcFilePropertiesInfo->uploadedFlag);
                        dcFileListRspPacket.dcFileInfo[sPacketIndex].osFileSize = SWAP64Ex(pFcFilePropertiesInfo->FileDetailInfoByTime.llOsFileSize);
                        if(getDcCtrlDbgStatus())
                        {
                            CRP_APP_INFO("sFileIndex:%03d sPacketIndex:%02d %64s %lldB %lldB deviceId:%d ch:%d keyDataFlag:%d downloadedFlag:%d", sFileIndex, sPacketIndex, 
                                          dcFileListRspPacket.dcFileInfo[sPacketIndex].pathname,
                                          pFcFilePropertiesInfo->FileDetailInfoByTime.llFileSize, pFcFilePropertiesInfo->FileDetailInfoByTime.llOsFileSize, FC_DEVICE_ID(getFcChassisId()),
                                          pFcFilePropertiesInfo->FileDetailInfoByTime.nChannel, pFcFilePropertiesInfo->keyDataFlag,
                                          pFcFilePropertiesInfo->uploadedFlag);
                        }
                        if((sPacketIndex == DC_PACKET_MAX_FILE_NUM-1) || (sFileIndex+1 == sFileTotalNumAll) || &pFcLsitNextNode->listHead == &pfcFileListInfo->fileList.listHead)
                        {
                            if(sFileIndex+1 == sFileTotalNumAll || &pFcLsitNextNode->listHead == &pfcFileListInfo->fileList.listHead) /* 当前为最后一个文件 */
                            {
                                dcFileListRspPacket.packetFlag = SWAP16Ex((uint16_x)DC_FILELIST_PACKET_END); /* 3：结束包 */
                                lastFlag = 1;
                            }
                            else /* 当前不是最后一个文件 */
                            {
                                if(firstFlag)
                                {
                                    dcFileListRspPacket.packetFlag = SWAP16Ex((uint16_x)DC_FILELIST_PACKET_START); /* 1：开始包 */
                                    firstFlag = 0;
                                }
                                else
                                {
                                    dcFileListRspPacket.packetFlag = SWAP16Ex((uint16_x)DC_FILELIST_PACKET_MIDDLE); /* 2：中间包 */
                                }
                            }
                            if(getDcCtrlDbgStatus())
                            {
                                CRP_APP_INFO("send packetFlag:%d fileNum:%02d",SWAP16Ex(dcFileListRspPacket.packetFlag),sPacketIndex+1);
                            }
                            fillDcRspCommunicateFrame(&DcRsp, &pReq->commonHead, 0, (uint8_x*)&dcFileListRspPacket, sizeof(uint16_x)+sizeof(uint16_x)+sizeof(DC_FILE_INFO)*(sPacketIndex+1));
                            sendDcCommunicateRsp(&DcRsp);
                            if(lastFlag)
                            {
                                break;
                            }
                        }
                        sFileIndex++;
                    }
                }
                fcFileListMutexUnlock();
            }
            ret = 0;
        }
    }

    if(ret != 0 || (ret == 0 && sFileTotalNumAll == 0))
    {
        dcFileListRspPacket.totalFileNum = SWAP16Ex(0); 
        dcFileListRspPacket.packetFlag = SWAP16Ex((uint16_x)DC_FILELIST_PACKET_END); /* 3：结束包 */
        if(getDcCtrlDbgStatus())
        {
            CRP_APP_INFO("send packetFlag:%d fileNum:%02d",SWAP16Ex(dcFileListRspPacket.packetFlag), SWAP16Ex(dcFileListRspPacket.totalFileNum));
        }
        fillDcRspCommunicateFrame(&DcRsp, &pReq->commonHead, 0, (uint8_x*)&dcFileListRspPacket, sizeof(uint16_x)+sizeof(uint16_x));
        sendDcCommunicateRsp(&DcRsp);
    }
    return ret;
}

/* 只针对固态 */
int dcSetLoopRecordReq(T_DcCommunicationReqList* pReqList)
{
    DC_COMMUNICATION_REQ *pReq;
    uint8_x checkSum;
    int nRecordDeviceTag;
    int ret[REC_MD_NUM];
    DC_COMMUNICATION_RSP DcRsp;
    int32_x chnMask[REC_MD_NUM];
    int busy[REC_MD_NUM] = {0,0};
    uint32_x workStatus;
    
    pReq = (DC_COMMUNICATION_REQ*)pReqList->pBuf;
    switchDcCommonReqBodyEndian(pReq);
    checkSum = calcCheckSum((uint8_x*)&pReq->commonHead.len, sizeof(DC_COMMUNICATION_REQ)-DC_HEAD_FLAG_LEN-1); 
    if(checkSum != pReq->checkSum)
    {
        CRP_APP_ERR("cmd %d invalid checkSum:%#x, should be %#x", pReq->commonHead.cmd, checkSum, pReq->checkSum);
        for(nRecordDeviceTag=0;nRecordDeviceTag<REC_MD_NUM;nRecordDeviceTag++)
        {
            ret[nRecordDeviceTag] = -1;
        }
    }
    else
    {
        if(getDcCtrlDbgStatus())
        {
            CRP_APP_INFO("%s(%#x): workMode1:%d DID1:%d DID2:%d DID3:%d DID4:%d DID5:%d DID6:%d DID7:%d DID8:%d DID9:%d DID10:%d DID11:%d DID12:%d DID13:%d DID14:%d DID15:%d DID16:%d", 
                         getCmdDesc(pReq->commonHead.cmd), pReq->commonHead.cmd, pReq->workMode1,
                         pReq->did[0], pReq->did[1], pReq->did[2], pReq->did[3], pReq->did[4], pReq->did[5], pReq->did[6], pReq->did[7], 
                         pReq->did[8], pReq->did[9], pReq->did[10], pReq->did[11], pReq->did[12], pReq->did[13], pReq->did[14], pReq->did[15]);
        }
        getBofeiChnMask(0, pReq, &chnMask[0]);
        getBofeiChnMask(1, pReq, &chnMask[1]);
        for(nRecordDeviceTag = 0; nRecordDeviceTag < REC_MD_NUM; nRecordDeviceTag++)
        {
            if(chnMask[nRecordDeviceTag])
            {
                if(getRecLiveStatus(nRecordDeviceTag))
                {
                    workStatus = getRecWorkStatus(nRecordDeviceTag);
                    if(workStatus == IDLE_STATUS)
                    {
                        ret[nRecordDeviceTag] = recRecyleRecordControlReq(nRecordDeviceTag, (pReq->workMode1)?1:0);
                    }
                    else
                    {
                        CRP_APP_ERR("nRecordDeviceTag:%d WorkStatus:%d, busy!", nRecordDeviceTag, workStatus);
                        ret[nRecordDeviceTag] = -1;
                        busy[nRecordDeviceTag] = 1;
                    }
                }
                else
                {
                    CRP_APP_ERR("nRecordDeviceTag:%d LiveStatus:%d", nRecordDeviceTag, getRecLiveStatus(nRecordDeviceTag));
                    ret[nRecordDeviceTag] = -1;
                }
            }
            else
            {
                ret[nRecordDeviceTag] = -1;
            }
        }
    }

    fillDcRspCommunicateFrame(&DcRsp, &pReq->commonHead, (ret[0]==0||ret[1]==0)?0:((busy[0]||busy[1])?DC_ERROR_NO_BUSY:-1), NULL, 0);
    sendDcCommunicateRsp(&DcRsp);
    return 0;
}

/* 只针对固态 */
int dcEmptyDataReq(T_DcCommunicationReqList* pReqList)
{
    DC_COMMUNICATION_REQ *pReq;
    uint8_x checkSum;
    int nRecordDeviceTag;
    int ret[REC_MD_NUM];
    DC_COMMUNICATION_RSP DcRsp;
    int busy[REC_MD_NUM] = {0, 0};
    uint32_x workStatus;
    int chnMask[REC_MD_NUM];
    int map;
    
    pReq = (DC_COMMUNICATION_REQ*)pReqList->pBuf;
    switchDcCommonReqBodyEndian(pReq);
    checkSum = calcCheckSum((uint8_x*)&pReq->commonHead.len, sizeof(DC_COMMUNICATION_REQ)-DC_HEAD_FLAG_LEN-1); 
    if(checkSum != pReq->checkSum)
    {
        CRP_APP_ERR("cmd %d invalid checkSum:%#x, should be %#x", pReq->commonHead.cmd, checkSum, pReq->checkSum);
        for(nRecordDeviceTag=0;nRecordDeviceTag<REC_MD_NUM;nRecordDeviceTag++)
        {
            ret[nRecordDeviceTag] = -1;
        }
    }
    else
    {
//        if(getDcCtrlDbgStatus())
        {
            CRP_APP_INFO("%s(%#x): DID1:%d DID2:%d DID3:%d DID4:%d DID5:%d DID6:%d DID7:%d DID8:%d DID9:%d DID10:%d DID11:%d DID12:%d DID13:%d DID14:%d DID15:%d DID16:%d", 
                         getCmdDesc(pReq->commonHead.cmd), pReq->commonHead.cmd, 
                         pReq->did[0], pReq->did[1], pReq->did[2], pReq->did[3], pReq->did[4], pReq->did[5], pReq->did[6], pReq->did[7], 
                         pReq->did[8], pReq->did[9], pReq->did[10], pReq->did[11], pReq->did[12], pReq->did[13], pReq->did[14], pReq->did[15]);
        }
        getBofeiChnMask(0, pReq, &chnMask[0]);
        getBofeiChnMask(1, pReq, &chnMask[1]);

        for(nRecordDeviceTag=0,map = 0;nRecordDeviceTag<REC_MD_NUM;nRecordDeviceTag++)
        {
            workStatus = getRecWorkStatus(nRecordDeviceTag);
            if(chnMask[nRecordDeviceTag] && getRecLiveStatus(nRecordDeviceTag) &&
               (workStatus == IDLE_STATUS || workStatus == DOWNLOAD_STATUS))
            {
                map |= (1<<nRecordDeviceTag);
            }
        }
        clearAutoDownloadIdleCountAll(map);
        
        for(nRecordDeviceTag = 0; nRecordDeviceTag < REC_MD_NUM; nRecordDeviceTag++)
        {
            if(chnMask[nRecordDeviceTag])
            {
                if(getRecLiveStatus(nRecordDeviceTag))
                {
                    workStatus = getRecWorkStatus(nRecordDeviceTag);
                    if(workStatus == IDLE_STATUS)
                    {
                        ret[nRecordDeviceTag] = recFormatConrtolReq(nRecordDeviceTag);
                    }
                    else
                    {
                        CRP_APP_ERR("nRecordDeviceTag:%d WorkStatus:%d, busy!", nRecordDeviceTag, workStatus);
                        ret[nRecordDeviceTag] = -1;
                        busy[nRecordDeviceTag] = 1;
                    }
                }
                else
                {
                    CRP_APP_ERR("nRecordDeviceTag:%d LiveStatus:%d", nRecordDeviceTag, getRecLiveStatus(nRecordDeviceTag));
                    ret[nRecordDeviceTag] = -1;
                }
            }
            else
            {
                ret[nRecordDeviceTag] = -1;
            }
        }
    }

    fillDcRspCommunicateFrame(&DcRsp, &pReq->commonHead, (ret[0]==0||ret[1]==0)?0:((busy[0]||busy[1])?DC_ERROR_NO_BUSY:-1), NULL, 0);
    sendDcCommunicateRsp(&DcRsp);
    return ret[0]|ret[1];
}

int dcStartStopDownloadReq(T_DcCommunicationReqList* pReqList)
{
    DC_COMMUNICATION_REQ *pReq;
    uint8_x checkSum;
    int nRecordDeviceTag;
    int ret;
    DC_COMMUNICATION_RSP DcRsp;
    int busy = 0;
    uint32_x workStatus;
    
    pReq = (DC_COMMUNICATION_REQ*)pReqList->pBuf;
    switchDcCommonReqBodyEndian(pReq);
    checkSum = calcCheckSum((uint8_x*)&pReq->commonHead.len, sizeof(DC_COMMUNICATION_REQ)-DC_HEAD_FLAG_LEN-1); 
    if(checkSum != pReq->checkSum)
    {
        CRP_APP_ERR("cmd %d invalid checkSum:%#x, should be %#x", pReq->commonHead.cmd, checkSum, pReq->checkSum);
        ret = -1;
    }
    else
    {
        if(getDcCtrlDbgStatus())
        {
            CRP_APP_INFO("%s(%#x): deviceIdOfFile:%#x visitFilePath:%s", getCmdDesc(pReq->commonHead.cmd), pReq->commonHead.cmd, pReq->deviceIdOfFile, pReq->visitFilePath);
        }
        nRecordDeviceTag = pReq->deviceIdOfFile - 1;
        if(nRecordDeviceTag == 0 || nRecordDeviceTag == 1)
        {
            if(getRecLiveStatus(nRecordDeviceTag))
            {
                workStatus = getRecWorkStatus(nRecordDeviceTag);
                if(pReq->commonHead.cmd == DC_CMD_START_DOWNLOAD) /* 开始转存 */
                {
                    if(workStatus == IDLE_STATUS || workStatus == DOWNLOAD_STATUS)
                    {
                        ret = clearAutoDownloadIdleCount(nRecordDeviceTag);
                        if(ret == 0)
                        {
                            ret = recDownloadControlOneFileReq(nRecordDeviceTag, SCF_START, (char*)pReq->visitFilePath, STD_MMOUNT_DIR);
                        }
                    }
                    else
                    {
                        CRP_APP_ERR("nRecordDeviceTag:%d WorkStatus:%d, busy!", nRecordDeviceTag, workStatus);
                        ret = -1;
                        busy = 1;
                    }
                }
                else if(pReq->commonHead.cmd == DC_CMD_STOP_DOWNLOAD) /* 停止转存 */
                {
                    if(workStatus == IDLE_STATUS || workStatus == DOWNLOAD_STATUS)
                    {
                        ret = recDownloadControlOneFileReq(nRecordDeviceTag, SCF_STOP, NULL, NULL);
                    }
                    else
                    {
                        CRP_APP_ERR("nRecordDeviceTag:%d WorkStatus:%d, busy!", nRecordDeviceTag, workStatus);
                        ret = -1;
                        busy = 1;
                    }
                }
                else
                {
                    ret = -1;
                }
            }
            else
            {
                CRP_APP_ERR("nRecordDeviceTag:%d LiveStatus:%d", nRecordDeviceTag, getRecLiveStatus(nRecordDeviceTag));
                ret = -1;
            }
        }
        else
        {
            CRP_APP_ERR("invalid deviceIdOfFile:%#x", nRecordDeviceTag);
            ret = -1;
        }
    }

    fillDcRspCommunicateFrame(&DcRsp, &pReq->commonHead, (ret==0)?0:((busy)?DC_ERROR_NO_BUSY:-1), NULL, 0);
    sendDcCommunicateRsp(&DcRsp);
    return ret;
}

int getChnFromFcFileName(const char* fileFullName) /*  mpo00_202304171243348, 0:无效通道 */
{
    char shortFileName[MAX_FILE_PATH_LEN_REC] = {0};
    int ret;
    int chn;
    uint32_x  year, month, day, hour, minute, second;
    
    ret = getFileNameFromFileFullName(fileFullName, shortFileName);
    if(ret)
    {
        CRP_APP_ERR("getFileNameFromFileFullName %s failed, ret = %d!", fileFullName, ret);
        return 0;
    }
    else
    {
        ret = sscanf(shortFileName, "mpo%02d_%04d%02d%02d%02d%02d%02d", &chn, &year, &month, &day, 
                     &hour, &minute, &second);
        if(ret!=7)
        {
            CRP_APP_ERR("sscanf %s failed, ret:%d != 7 !", shortFileName, ret);
            return 0;
        }
        else
        {
            return chn;
        }
    }
}

int dcStartStopUploadReq(T_DcCommunicationReqList* pReqList)
{
    DC_UPLOAD_REQ *pReq;
    uint8_x checkSum;
    int nRecordDeviceTag;
    int ret[REC_MD_NUM];
    DC_COMMUNICATION_RSP DcRsp;
    int i;
    tagUploadCtrlMsgReq UploadCtrlMsgReq[REC_MD_NUM];
    T_FcFilePropertiesInfo FcFilePropertiesInfo;
    int chn;
    int busy[REC_MD_NUM] = {0,0};
    uint32_x workStatus;
    int map;
    
    pReq = (DC_UPLOAD_REQ*)pReqList->pBuf;
    switchDcUploadReqBodyEndian(pReq);
    checkSum = calcCheckSum((uint8_x*)&pReq->commonHead.len, sizeof(DC_UPLOAD_REQ)-DC_HEAD_FLAG_LEN-1); 
    if(checkSum != pReq->checkSum)
    {
        CRP_APP_ERR("cmd %d invalid checkSum:%#x, should be %#x", pReq->commonHead.cmd, checkSum, pReq->checkSum);
        for(nRecordDeviceTag=0;nRecordDeviceTag<REC_MD_NUM;nRecordDeviceTag++)
        {
            ret[nRecordDeviceTag] = -1;
        }
    }
    else
    {
        if(getDcCtrlDbgStatus())
        {
            CRP_APP_INFO("%s(%#x):", getCmdDesc(pReq->commonHead.cmd), pReq->commonHead.cmd);
            for(i=0; i<DC_MAX_UPLOAD_FILE_COUNT; i++)
            {
                CRP_APP_INFO("%d en:%d filePath:%s destDeviceId:%#x", i, pReq->uploadChnInfo[i].en, pReq->uploadChnInfo[i].filePath, pReq->uploadChnInfo[i].destDeviceId);
            }
        }
        if(pReq->commonHead.cmd == DC_CMD_START_UPLOAD) /* 开始转存 */
        {
            memset(UploadCtrlMsgReq, 0, sizeof(UploadCtrlMsgReq));
            for(i=0; i<DC_MAX_UPLOAD_FILE_COUNT; i++)
            {
                if(pReq->uploadChnInfo[i].en)
                {
                    if(getFcFilePropertiesInfoByFileFullName(pReq->uploadChnInfo[i].filePath, &FcFilePropertiesInfo)==0)
                    {
                        chn = FcFilePropertiesInfo.FileDetailInfoByTime.nChannel;
                    }
                    else
                    {
                        chn = getChnFromFcFileName(pReq->uploadChnInfo[i].filePath);
                    }
                    if(chn<1 || chn>DC_CHN_NUM)
                    {
                        CRP_APP_ERR("%s get chn failed, ch:%d", pReq->uploadChnInfo[i].filePath, chn);
                        nRecordDeviceTag = -1;
                    }
                    else
                    {
                        nRecordDeviceTag = (chn<=8)?0:1;
                    }
                    if(nRecordDeviceTag!=0 && nRecordDeviceTag!=1)
                    {
                        CRP_APP_ERR("i:%d invalid destDeviceId %d", i, nRecordDeviceTag);
                    }
                    else if(strlen(pReq->uploadChnInfo[i].filePath)<strlen(STD_MMOUNT_DIR))
                    {
                        CRP_APP_ERR("i:%d invalid filePath %s", i, pReq->uploadChnInfo[i].filePath);
                    }
                    else
                    {
                        strcpy(UploadCtrlMsgReq[nRecordDeviceTag].strSourceFilePath[UploadCtrlMsgReq[nRecordDeviceTag].sFileCount], pReq->uploadChnInfo[i].filePath); 
                        UploadCtrlMsgReq[nRecordDeviceTag].sFileCount++;
                    }
                }
            }
            for(nRecordDeviceTag=0,map = 0;nRecordDeviceTag<REC_MD_NUM;nRecordDeviceTag++)
            {
                workStatus = getRecWorkStatus(nRecordDeviceTag);
                if(UploadCtrlMsgReq[nRecordDeviceTag].sFileCount>0 && getRecLiveStatus(nRecordDeviceTag) &&
                   (workStatus == IDLE_STATUS || workStatus == DOWNLOAD_STATUS))
                {
                    map |= (1<<nRecordDeviceTag);
                }
            }
            clearAutoDownloadIdleCountAll(map);
            for(nRecordDeviceTag=0;nRecordDeviceTag<REC_MD_NUM;nRecordDeviceTag++)
            {
                if(UploadCtrlMsgReq[nRecordDeviceTag].sFileCount>0)
                {
                    if(getRecLiveStatus(nRecordDeviceTag))
                    {
                        workStatus = getRecWorkStatus(nRecordDeviceTag);
                        if(workStatus == IDLE_STATUS)
                        {
                            ret[nRecordDeviceTag] = recUploadControlReq(nRecordDeviceTag, UCF_START_UPLOAD, REC_UPLOAD_DIR, UploadCtrlMsgReq[nRecordDeviceTag].sFileCount, UploadCtrlMsgReq[nRecordDeviceTag].strSourceFilePath);     
                        }
                        else
                        {
                            CRP_APP_ERR("nRecordDeviceTag:%d WorkStatus:%d, busy!", nRecordDeviceTag, workStatus);
                            ret[nRecordDeviceTag] = -1;
                            busy[nRecordDeviceTag] = 1;
                        }
                    }
                    else
                    {
                        CRP_APP_ERR("nRecordDeviceTag:%d LiveStatus:%d", nRecordDeviceTag, getRecLiveStatus(nRecordDeviceTag));
                        ret[nRecordDeviceTag] = -1;
                    }
                }
                else
                {
                    ret[nRecordDeviceTag] = -1;
                }
            }
        }
        else if(pReq->commonHead.cmd == DC_CMD_STOP_UPLOAD) /* 停止转存 */
        {
            for(nRecordDeviceTag=0;nRecordDeviceTag<REC_MD_NUM;nRecordDeviceTag++)
            {
                if(getRecLiveStatus(nRecordDeviceTag))
                {
                    workStatus = getRecWorkStatus(nRecordDeviceTag);
                    if(workStatus == IDLE_STATUS || workStatus == UPLOAD_STATUS)
                    {
                        ret[nRecordDeviceTag] = recUploadControlReq(nRecordDeviceTag, UCF_STOP_UPLOAD, REC_UPLOAD_DIR, 0, NULL);      
                    }
                    else
                    {
                        CRP_APP_ERR("nRecordDeviceTag:%d WorkStatus:%d, busy!", nRecordDeviceTag, workStatus);
                        ret[nRecordDeviceTag] = -1;
                        busy[nRecordDeviceTag] = 1;
                    }
                }
                else
                {
                    CRP_APP_ERR("nRecordDeviceTag:%d LiveStatus:%d", nRecordDeviceTag, getRecLiveStatus(nRecordDeviceTag));
                    ret[nRecordDeviceTag] = -1;
                }
            }
        }
        else
        {
            for(nRecordDeviceTag=0;nRecordDeviceTag<REC_MD_NUM;nRecordDeviceTag++)
            {
                ret[nRecordDeviceTag] = -1;
            }
        }
    }
    fillDcRspCommunicateFrame(&DcRsp, &pReq->commonHead, (ret[0]==0||ret[1]==0)?0:((busy[0]||busy[1])?DC_ERROR_NO_BUSY:-1), NULL, 0);
    sendDcCommunicateRsp(&DcRsp);
    return ret[0]|ret[1];
}

int fcDeleteFileTask(tagDeleteCtrlMsgReq *pFcDeleteCtrlMsgReq)
{
    int ret;
    int i;

    prctl(PR_SET_NAME, "fcDeleteFileTask");
    setFcDeleteStatus(1);    
    if(pFcDeleteCtrlMsgReq && pFcDeleteCtrlMsgReq->sFileCount<=MAX_DELETE_LIST_COUNT)
    {
        for(i=0;i<pFcDeleteCtrlMsgReq->sFileCount;i++)
        {
            delFilePropertiesInfoAndDataBase(pFcDeleteCtrlMsgReq->strFileNamePathList[i]);
            ret = delFcFileByFullName(pFcDeleteCtrlMsgReq->strFileNamePathList[i]);
            dcDeleteFcFileStateReport(pFcDeleteCtrlMsgReq->strFileNamePathList[i], 100, ret);
//            CRP_APP_INFO("%d: delete fc file %s sFileTotalCount:%d", i, pFcDeleteCtrlMsgReq->strFileNamePathList[i], pFcDeleteCtrlMsgReq->sFileCount);
        }
    }
//    CRP_APP_INFO("%d: sFileTotalCount:%d", i, pFcDeleteCtrlMsgReq->sFileCount);
    if(pFcDeleteCtrlMsgReq)
    {
        free(pFcDeleteCtrlMsgReq);
    }
    setFcDeleteStatus(0);
    return 0;
}

int dcDeleteFileReq(T_DcCommunicationReqList* pReqList)
{
    DC_DELETE_FILE_REQ *pReq;
    uint8_x checkSum;
    int nRecordDeviceTag;
    int ret[REC_MD_NUM+1], retFc = 0, retAll;
    DC_COMMUNICATION_RSP DcRsp;
    int i;
    tagDeleteCtrlMsgReq DeleteCtrlMsgReq[REC_MD_NUM+1];
    tagDeleteCtrlMsgReq *pFcDeleteCtrlMsgReq = NULL;
    int busy[REC_MD_NUM] = {0,0};
    uint32_x workStatus;
    int map;
    
    pReq = (DC_DELETE_FILE_REQ*)pReqList->pBuf;
    switchDcDeleteReqBodyEndian(pReq);
    checkSum = calcCheckSum((uint8_x*)&pReq->commonHead.len, sizeof(DC_DELETE_FILE_REQ)-DC_HEAD_FLAG_LEN-1); 
    if(checkSum != pReq->checkSum)
    {
        CRP_APP_ERR("cmd %d invalid checkSum:%#x, should be %#x", pReq->commonHead.cmd, checkSum, pReq->checkSum);
        for(nRecordDeviceTag=0;nRecordDeviceTag<REC_MD_NUM;nRecordDeviceTag++)
        {
            ret[nRecordDeviceTag] = -1;
            retFc = -1;
        }
    }
    else
    {
        if(getDcCtrlDbgStatus())
        {
            CRP_APP_INFO("%s(%#x):", getCmdDesc(pReq->commonHead.cmd), pReq->commonHead.cmd);
            for(i=0; i<DC_MAX_DELETE_FILE_COUNT; i++)
            {
                CRP_APP_INFO("%d en:%d filePath:%s deviceIdOfFile:%#x", i, pReq->delCmdInfo[i].en, pReq->delCmdInfo[i].filePath, pReq->delCmdInfo[i].deviceIdOfFile);
            }
        }
        pFcDeleteCtrlMsgReq = (tagDeleteCtrlMsgReq*)malloc(sizeof(tagDeleteCtrlMsgReq));
        if(pFcDeleteCtrlMsgReq == NULL)
        {
            retFc = -1;
        }
        else
        {
            memset(pFcDeleteCtrlMsgReq, 0, sizeof(tagDeleteCtrlMsgReq));
        }
        memset(DeleteCtrlMsgReq, 0, sizeof(DeleteCtrlMsgReq));
        for(i=0; i<DC_MAX_DELETE_FILE_COUNT; i++)
        {
            if(pReq->delCmdInfo[i].en)
            {
                nRecordDeviceTag = pReq->delCmdInfo[i].deviceIdOfFile - 1;
                if(nRecordDeviceTag == 0 || nRecordDeviceTag == 1)
                {
                    if(strlen(pReq->delCmdInfo[i].filePath)<strlen(REC_UPLOAD_DIR))
                    {
                        CRP_APP_ERR("i:%d invalid filePath %s", i, pReq->delCmdInfo[i].filePath);
                    }
                    else
                    {
                        strcpy(DeleteCtrlMsgReq[nRecordDeviceTag].strFileNamePathList[DeleteCtrlMsgReq[nRecordDeviceTag].sFileCount], pReq->delCmdInfo[i].filePath); 
                        DeleteCtrlMsgReq[nRecordDeviceTag].sFileCount++;
                    }
                }
                else if(pReq->delCmdInfo[i].deviceIdOfFile == FC_DEVICE_ID(getFcChassisId()))
                {
                    if(pFcDeleteCtrlMsgReq)
                    {
                        if(isFileExist(pReq->delCmdInfo[i].filePath))
                        {
                            strcpy(pFcDeleteCtrlMsgReq->strFileNamePathList[pFcDeleteCtrlMsgReq->sFileCount], pReq->delCmdInfo[i].filePath); 
                            pFcDeleteCtrlMsgReq->sFileCount++;
//                            CRP_APP_INFO("i:%d sFileCount:%d file %s!", i, pFcDeleteCtrlMsgReq->sFileCount, pReq->delCmdInfo[i].filePath);
                        }
                        else
                        {
                            CRP_APP_ERR("i:%d file %s is not exist!", i,  pReq->delCmdInfo[i].filePath);
                        }
                    }
                }
                else
                {
                    CRP_APP_ERR("i:%d invalid deviceIdOfFile %d", i, pReq->delCmdInfo[i].deviceIdOfFile);
                }
            }
        }
        
        for(nRecordDeviceTag=0,map = 0;nRecordDeviceTag<REC_MD_NUM;nRecordDeviceTag++)
        {
            workStatus = getRecWorkStatus(nRecordDeviceTag);
            if(DeleteCtrlMsgReq[nRecordDeviceTag].sFileCount>0 && getRecLiveStatus(nRecordDeviceTag) &&
               (workStatus == IDLE_STATUS || workStatus == DOWNLOAD_STATUS))
            {
                map |= (1<<nRecordDeviceTag);
            }
        }
        clearAutoDownloadIdleCountAll(map);
        for(nRecordDeviceTag=0;nRecordDeviceTag<REC_MD_NUM;nRecordDeviceTag++)
        {
            if(DeleteCtrlMsgReq[nRecordDeviceTag].sFileCount>0)
            {
                if(getRecLiveStatus(nRecordDeviceTag))
                {
                    workStatus = getRecWorkStatus(nRecordDeviceTag);
                    if(workStatus == IDLE_STATUS)
                    {
                        ret[nRecordDeviceTag] = recDeleteFileControlReq(nRecordDeviceTag, DeleteCtrlMsgReq[nRecordDeviceTag].sFileCount, DeleteCtrlMsgReq[nRecordDeviceTag].strFileNamePathList);          
                    }
                    else
                    {
                        CRP_APP_ERR("nRecordDeviceTag:%d WorkStatus:%d, busy!", nRecordDeviceTag, workStatus);
                        ret[nRecordDeviceTag] = -1;
                        busy[nRecordDeviceTag] = 1;
                    }
                }
                else
                {
                    CRP_APP_ERR("nRecordDeviceTag:%d LiveStatus:%d", nRecordDeviceTag, getRecLiveStatus(nRecordDeviceTag));
                    ret[nRecordDeviceTag] = -1;
                }
            }
            else
            {
                ret[nRecordDeviceTag] = -1;
            }
        }
        if(pFcDeleteCtrlMsgReq)
        {
            if(pFcDeleteCtrlMsgReq->sFileCount>0)
            {
                retFc = threadCreate((THREAD_PROC)fcDeleteFileTask, pFcDeleteCtrlMsgReq, "fcDeleteFileTask", 0, NULL);
                if (retFc < 0)
                {
                    free(pFcDeleteCtrlMsgReq);
                    CRP_APP_ERR("err:%s create failed ret=%d","fcDeleteFileTask", retFc);
                }
            }
            else
            {
                free(pFcDeleteCtrlMsgReq);
            }
        }
    }
    retAll = (ret[0]==0 || ret[1]==0 || retFc==0)?0:-1;
    fillDcRspCommunicateFrame(&DcRsp, &pReq->commonHead, (retAll && (busy[0] || busy[1]))?DC_ERROR_NO_BUSY:retAll, NULL, 0);
//    showDcRspMsgHead(&DcRsp.commonHead);
    sendDcCommunicateRsp(&DcRsp);
    return retAll;
}

T_DcCommunicationProcOpHdl g_DcCommunicationProcOpHdl[]=
{
    [DC_CMD_START_RECORD]      ={DC_CMD_START_RECORD,               dcStartRecordReq, sizeof(DC_COMMUNICATION_REQ), "开始记录"},            /* 开始记录 */
    [DC_CMD_STOP_RECORD]       ={DC_CMD_STOP_RECORD,                 dcStopRecordReq, sizeof(DC_COMMUNICATION_REQ), "停止记录"},            /* 停止记录 */
    [DC_CMD_RECYLE_REPLAY]     ={DC_CMD_RECYLE_REPLAY,          dcStartStopReplayReq, sizeof(DC_COMMUNICATION_REQ), "循环回放"},            /* 循环回放 */
    [DC_CMD_START_REPLAY]      ={DC_CMD_START_REPLAY,           dcStartStopReplayReq, sizeof(DC_COMMUNICATION_REQ), "开始回放"},            /* 开始回放 */
    [DC_CMD_STOP_REPLAY]       ={DC_CMD_STOP_REPLAY,            dcStartStopReplayReq, sizeof(DC_COMMUNICATION_REQ), "停止回放"},            /* 停止回放 */
    [DC_CMD_GET_FILE_LIST]     ={DC_CMD_GET_FILE_LIST,              dcGetFileListReq, sizeof(DC_COMMUNICATION_REQ), "获取文件列表"},          /* 获取文件列表 */
    [DC_CMD_SET_LOOP_RECORD]   ={DC_CMD_SET_LOOP_RECORD,          dcSetLoopRecordReq, sizeof(DC_COMMUNICATION_REQ), "设置循环记录"},          /* 设置循环记录 */
    [DC_CMD_EMPTY_DATA]        ={DC_CMD_EMPTY_DATA,                   dcEmptyDataReq, sizeof(DC_COMMUNICATION_REQ), "清空数据"},            /* 清空数据 */
    [DC_CMD_START_DOWNLOAD]    ={DC_CMD_START_DOWNLOAD,       dcStartStopDownloadReq, sizeof(DC_COMMUNICATION_REQ), "开始转储数据"},          /* 开始转储数据 */
    [DC_CMD_STOP_DOWNLOAD]     ={DC_CMD_STOP_DOWNLOAD,        dcStartStopDownloadReq, sizeof(DC_COMMUNICATION_REQ), "停止转储数据"},          /* 停止转储数据 */
    [DC_CMD_START_UPLOAD]      ={DC_CMD_START_UPLOAD,           dcStartStopUploadReq, sizeof(DC_UPLOAD_REQ),        "开始上传数据"},          /* 开始上传数据 */
    [DC_CMD_STOP_UPLOAD]       ={DC_CMD_STOP_UPLOAD,            dcStartStopUploadReq, sizeof(DC_UPLOAD_REQ),        "停止上传数据"},          /* 停止上传数据 */  
    [DC_CMD_DELETE_FILE]       ={DC_CMD_DELETE_FILE,                 dcDeleteFileReq, sizeof(DC_DELETE_FILE_REQ),   "删除文件"}             /* 删除文件 */
}; 

/* 待修改BIT结果和工作状态 */
int dcStorageStateReport(void)
{
    int ret;
    DC_WHOLE_DEVICE_STATUS_RSP StatusInfo;
    DC_COMMUNICATION_RSP DcRsp;
    unsigned int nRecordDeviceTag;
    tagDeviceStatusMsgResp DeviceStatusMsg;

    memset(&StatusInfo, 0, sizeof(StatusInfo));
    StatusInfo.llTotalCapacityFc = SWAP64Ex(getDiskTotalSpace(0, NULL)*1024ull*1024);
    StatusInfo.llFreeCapacityFc = SWAP64Ex(getDirFreeCapacity(STD_MMOUNT_DIR)*1024ull*1024);
    StatusInfo.nWorkStatusFc = SWAP32Ex(getFcWorkStatus());/* 待修改BIT结果和工作状态 */
    StatusInfo.nBitResultFc = SWAP32Ex(0);/* 待修改BIT结果和工作状态 */
    for(nRecordDeviceTag=0;nRecordDeviceTag<REC_MD_NUM;nRecordDeviceTag++)
    {
        if(getRecLiveStatus(nRecordDeviceTag))
        {
            getRecDeviceStatus(nRecordDeviceTag, &DeviceStatusMsg);
            StatusInfo.recMdStatusInfo[nRecordDeviceTag].llRecTotalCapacity = SWAP64Ex(DeviceStatusMsg.nTotalCapacity*1024ull*1024);
            StatusInfo.recMdStatusInfo[nRecordDeviceTag].llRecFreeCapacity = SWAP64Ex(DeviceStatusMsg.nFreeCapacity*1024ull*1024);
            StatusInfo.recMdStatusInfo[nRecordDeviceTag].nRecWorkStatus = SWAP32Ex(DeviceStatusMsg.nWorkStatus);
            StatusInfo.recMdStatusInfo[nRecordDeviceTag].nRecCpuTemp = SWAP32Ex(DeviceStatusMsg.nCpuTemp);
            StatusInfo.recMdStatusInfo[nRecordDeviceTag].nRecFpgaTemp = SWAP32Ex(DeviceStatusMsg.nFpgaTemp);
            StatusInfo.recMdStatusInfo[nRecordDeviceTag].nRecLinkStatus = SWAP32Ex(DeviceStatusMsg.nLinkStatus);
        }
#if 0
        else
        {
            CRP_APP_ERR("nRecordDeviceTag:%d is offline!", nRecordDeviceTag);
        }
#endif
    }
    fillDcCommunicateReportFrame(&DcRsp, DC_CMD_REPORT_FC_STORAGE_STATE, 0, (uint8_x*)&StatusInfo, sizeof(StatusInfo));
    ret = sendDcCommunicateRsp(&DcRsp);
#ifdef HARDWARE_VER_V2
    if(!ret)
    {
        system("memtool mw 0x20002050 0x1");
    }
    else
    {
        CRP_APP_ERR("ret: %d, sendDcCommunicate failed", ret);
    }
#endif
    return ret;
}

int dcRecordStateReport(int nRecordDeviceTag, void *pOutRecordStateMsg)
{
    tagRecordStateMsgResp *pOutRecordState = (tagRecordStateMsgResp *)pOutRecordStateMsg;
    DC_RECORD_STATUS_RSP StatusInfo;
    DC_COMMUNICATION_RSP DcRsp;
    int i, reportChnNum = 0;

    memset(&StatusInfo, 0, sizeof(StatusInfo));
    reportChnNum = 0;
//    if(pOutRecordState->iRet == 0)
    {
        for(i=0;i<MAX_RECORD_CHANNEL_NUM;i++)
        {
            if(pOutRecordState->tChannelRecordingStateList[i].sRecordState&0x3)
            {
                if(i<4 || i>=8)
                {
                    if(getDcCtrlDbgStatus())
                    {
                        CRP_APP_INFO("nRecordDeviceTag:%d chn:%d-%d  sRecordSpeed:%dMB/s nRecordSize:%dMB",
                                     nRecordDeviceTag, i+1, getDcChn(nRecordDeviceTag, i+1),
                                     pOutRecordState->tChannelRecordingStateList[i].sRecordSpeed, pOutRecordState->tChannelRecordingStateList[i].nRecordSize);
                    }
                    StatusInfo.chnStatusInfo[reportChnNum].ch = SWAP16Ex(getDcChn(nRecordDeviceTag, i+1));
                    StatusInfo.chnStatusInfo[reportChnNum].speed = SWAP16Ex(pOutRecordState->tChannelRecordingStateList[i].sRecordSpeed);
                    StatusInfo.chnStatusInfo[reportChnNum].size = SWAP32Ex(pOutRecordState->tChannelRecordingStateList[i].nRecordSize);
                    reportChnNum++;
                }
            }
        }
    }
    
    StatusInfo.reportChnNum = SWAP16Ex(reportChnNum);
    fillDcCommunicateReportFrame(&DcRsp, DC_CMD_REPORT_FC_RECORD_STATE, (pOutRecordState->iRet)?-1:0, (uint8_x*)&StatusInfo, 
                                 sizeof(uint16_x)+sizeof(CHN_RECORD_STATUS_INFO)*reportChnNum);
//    CRP_APP_INFO("send nRecordDeviceTag:%d reportChnNum:%d iRet:%d", nRecordDeviceTag, reportChnNum, pOutRecordState->iRet);
    sendDcCommunicateRsp(&DcRsp);
    return 0;
}

int dcReplayStateReport(int nRecordDeviceTag, void *pOutReplayStateMsg)
{
    tagReplayStateMsgResp *pOutReplayState = (tagReplayStateMsgResp *)pOutReplayStateMsg;
    DC_REPLAY_STATUS_RSP StatusInfo;
    DC_COMMUNICATION_RSP DcRsp;
    int i, reportChnNum = 0;

    memset(&StatusInfo, 0, sizeof(StatusInfo));
    reportChnNum = 0;
    if(pOutReplayState->iRet == 0)
    {
        for(i=0;i<MAX_RECORD_CHANNEL_NUM;i++)
        {
            if(pOutReplayState->tReplayChannelInfoList[i].sReplayState&0x3)
            {
                if(i<4 || i>=8)
                {
                    StatusInfo.chnStatusInfo[reportChnNum].ch = SWAP16Ex(getDcChn(nRecordDeviceTag, i+1));
                    StatusInfo.chnStatusInfo[reportChnNum].speed = SWAP16Ex(pOutReplayState->tReplayChannelInfoList[i].sReplaySpeed);
                    StatusInfo.chnStatusInfo[reportChnNum].size = SWAP32Ex(pOutReplayState->tReplayChannelInfoList[i].iReplayFileSize);
                    reportChnNum++;
                }
            }
        }
    }
    
    StatusInfo.reportChnNum = SWAP16Ex(reportChnNum);
    fillDcCommunicateReportFrame(&DcRsp, DC_CMD_REPORT_FC_REPLAY_STATE, (pOutReplayState->iRet)?-1:0, (uint8_x*)&StatusInfo, 
                                 sizeof(uint16_x)+sizeof(CHN_REPLAY_STATUS_INFO)*reportChnNum);
    sendDcCommunicateRsp(&DcRsp);
    return 0;
}

int dcDownloadStateReport(int nRecordDeviceTag, void *pOutDownloadStateMsg)
{
    tagDownloadStateMsgResp *pOutDownloadState = (tagDownloadStateMsgResp *)pOutDownloadStateMsg;
    DC_DOWNLOAD_STATUS_RSP StatusInfo;
    DC_COMMUNICATION_RSP DcRsp;

    memset(&StatusInfo, 0, sizeof(StatusInfo));
    StatusInfo.curOpeFileProgress = SWAP32Ex(pOutDownloadState->iCurOpeFileProgress);
    strncpy(StatusInfo.strFileNamePath, pOutDownloadState->strFileNamePath, sizeof(StatusInfo.strFileNamePath)-1);
    StatusInfo.strFileNamePath[sizeof(StatusInfo.strFileNamePath)-1] = 0;
    fillDcCommunicateReportFrame(&DcRsp, DC_CMD_REPORT_FC_DOWNLOAD_STATE, (pOutDownloadState->iRet)?-1:0, (uint8_x*)&StatusInfo, sizeof(StatusInfo));
    sendDcCommunicateRsp(&DcRsp);
    return 0;
}

int dcUploadStateReport(int nRecordDeviceTag, void *pOutUploadStateMsg)
{
    tagUploadStateMsgResp *pOutUploadState = (tagUploadStateMsgResp *)pOutUploadStateMsg;
    DC_UPLOAD_STATUS_RSP StatusInfo;
    DC_COMMUNICATION_RSP DcRsp;

    memset(&StatusInfo, 0, sizeof(StatusInfo));
    StatusInfo.curOpeFileProgress = SWAP32Ex(pOutUploadState->iUploadProgress);
    strncpy(StatusInfo.strFileNamePath, pOutUploadState->strCurFilePath, sizeof(StatusInfo.strFileNamePath)-1);
    StatusInfo.strFileNamePath[sizeof(StatusInfo.strFileNamePath)-1] = 0;
    fillDcCommunicateReportFrame(&DcRsp, DC_CMD_REPORT_FC_UPLOAD_STATE, (pOutUploadState->iRet)?-1:0, (uint8_x*)&StatusInfo, sizeof(StatusInfo));
    sendDcCommunicateRsp(&DcRsp);
    return 0;
}

int dcDeleteStateReport(int nRecordDeviceTag, void *pOutDeleteStateMsg)
{
    tagDeleteStateMsgResq *pDeleteStateMsgResq = (tagDeleteStateMsgResq *)pOutDeleteStateMsg;
    DC_DELETE_STATUS_RSP StatusInfo;
    DC_COMMUNICATION_RSP DcRsp;

    memset(&StatusInfo, 0, sizeof(StatusInfo));
    StatusInfo.curOpeFileProgress = SWAP32Ex(pDeleteStateMsgResq->iCurOpeFileProgress);
    StatusInfo.deviceIdOfcurOpeFile = SWAP32Ex(nRecordDeviceTag+1);
    strncpy(StatusInfo.strFileNamePath, pDeleteStateMsgResq->strCurFilePath, sizeof(StatusInfo.strFileNamePath)-1);
    StatusInfo.strFileNamePath[sizeof(StatusInfo.strFileNamePath)-1] = 0;
    fillDcCommunicateReportFrame(&DcRsp, DC_CMD_REPORT_FC_DELETE_STATE, (pDeleteStateMsgResq->iRet)?-1:0, (uint8_x*)&StatusInfo, sizeof(StatusInfo));
    sendDcCommunicateRsp(&DcRsp);
    return 0;
}

int dcDeleteFcFileStateReport(char* strFileNamePath, uint32_x nCurOpeFileProgress, int nRet)
{
    DC_DELETE_STATUS_RSP StatusInfo;
    DC_COMMUNICATION_RSP DcRsp;

    memset(&StatusInfo, 0, sizeof(StatusInfo));
    StatusInfo.curOpeFileProgress = SWAP32Ex(nCurOpeFileProgress);
    StatusInfo.deviceIdOfcurOpeFile = SWAP32Ex(FC_DEVICE_ID(getFcChassisId()));
    strncpy(StatusInfo.strFileNamePath, strFileNamePath, sizeof(StatusInfo.strFileNamePath)-1);
    StatusInfo.strFileNamePath[sizeof(StatusInfo.strFileNamePath)-1] = 0;
    fillDcCommunicateReportFrame(&DcRsp, DC_CMD_REPORT_FC_DELETE_STATE, (nRet)?-1:0, (uint8_x*)&StatusInfo, sizeof(StatusInfo));
    sendDcCommunicateRsp(&DcRsp);
    return 0;
}

typedef struct tag_Fc_Device_Status
{
    int nWorkStatus;//1表示空闲,2表示记录,3表示回放,4表示转存,5表示导入,6表示删除,其他表示无效
    void* mutex;
}FcDeviceStatus;

FcDeviceStatus g_FcDeviceStatus = {1, NULL};

unsigned int getFcWorkStatus(void)
{
    T_DcCommunicationPara* pDcCommunicationPara = &g_DcCommunicationPara;
    int nWorkStatus;

    mutexLock(pDcCommunicationPara->workStatusMutex, -1);
    nWorkStatus = pDcCommunicationPara->workStatus;
    mutexUnlock(pDcCommunicationPara->workStatusMutex);
    return nWorkStatus;
}

int setFcWorkStatus(unsigned int nWorkStatus)
{
    T_DcCommunicationPara* pDcCommunicationPara = &g_DcCommunicationPara;

    mutexLock(pDcCommunicationPara->workStatusMutex, -1);
    pDcCommunicationPara->workStatus = nWorkStatus;
    mutexUnlock(pDcCommunicationPara->workStatusMutex);
    return 0;
}

unsigned int getFcDeleteStatus(void)
{
    T_DcCommunicationPara* pDcCommunicationPara = &g_DcCommunicationPara;
    unsigned int nDeleteStatus;
    
    mutexLock(pDcCommunicationPara->workStatusMutex, -1);
    nDeleteStatus = pDcCommunicationPara->deleteStatus;
    mutexUnlock(pDcCommunicationPara->workStatusMutex);
    return nDeleteStatus;
}

int setFcDeleteStatus(unsigned int en)
{
    T_DcCommunicationPara* pDcCommunicationPara = &g_DcCommunicationPara;

    mutexLock(pDcCommunicationPara->workStatusMutex, -1);
    pDcCommunicationPara->deleteStatus = en;
    mutexUnlock(pDcCommunicationPara->workStatusMutex);
    return 0;
}

void setSystemTime(uint32_t mPassed)
{
    time_t timer, nowSet;
    struct tm *ptm;
    char setCmd[100];

    nowSet = get_cur_s();
    timer = (time_t)(SWAP32(mPassed)/1000 + getPassedSecondFrom0());  
    CRP_APP_INFO("recv mPassed %d timer : %lld, nowSet: %lld, abs: %lld", mPassed, timer, nowSet, abs(nowSet - timer));
    if(abs(nowSet-timer) < 5 || abs(nowSet-timer) > 1000)
    {
        CRP_APP_INFO("time diff so far, give up setting time");
        return;
    }
    
    ptm = localtime(&timer); // 将时间戳转化为tm
    sprintf(setCmd,"date -s \"%04d-%02d-%02d %02d:%02d:%02d\"",ptm->tm_year + 1900,ptm->tm_mon + 1,ptm->tm_mday,ptm->tm_hour, ptm->tm_min, ptm->tm_sec);
    CRP_APP_INFO("%s", setCmd);
    system(setCmd);
    giveTimePowerOn();//54508000
    system("hwclock -w");
}


#define TEMP_BUF_SIZE (1*1024*1024)
int dcCommunicationProcTask(void)
{
    T_DcCommunicationPara* pDcCommunicationPara = &g_DcCommunicationPara;
    int ret;
    T_DcCommunicationReqList* pLsitNode;
    uint8_x i;
    DC_REQ_FRAME_COMMON_HEAD* pReqHead;
    CRP_APP_INFO("dcCommunicate");
    prctl(PR_SET_NAME, "dcCommunicationProcTask");
    while(1)
    {
        if(pDcCommunicationPara->exitFlag == 1)
        {
            break;
        }
        if (pDcCommunicationPara->runFlag == 0)
        {
            mdelay(1);
            continue;
        }
        else
        {
        	
            ret = getFirstDcCommunicationListNode(&pLsitNode);
            if(ret)
            {
                mdelay(10);
            }
            else
            {
                pReqHead = (DC_REQ_FRAME_COMMON_HEAD*)pLsitNode->pBuf;
                switchDcReqMsgHeadEndian(pReqHead);
                if(pReqHead->cmd == DC_CMD_START_RECORD)
                {
                    CRP_APP_INFO("get DC_CMD_START_RECORD");
                    setSystemTime(pReqHead->msPassed);
                }
                showDcReqMsgHead(pReqHead);
                for(i = 0; i < DC_HEAD_FLAG_LEN; i++)
                {
                    if(pReqHead->head[i] != DC_REQ_HEAD_FLAG)
                    {
                        break;
                    }
                }
                if(i < DC_HEAD_FLAG_LEN)
                {
                    CRP_APP_ERR("invalid head[%d]:%#x, should be %#x", pReqHead->head[i], DC_REQ_HEAD_FLAG);
                }
                else
                {
                    if(pReqHead->fcId != FC_DEVICE_ID(getFcChassisId()) && 
                        pReqHead->cmd != DC_CMD_START_RECORD && 
                        pReqHead->cmd != DC_CMD_STOP_RECORD )
                    {
                        CRP_APP_ERR("invalid fcId:%#x, should be %#x", pReqHead->fcId, FC_DEVICE_ID(getFcChassisId()));
                    }
                    else if(pReqHead->cmd > DC_CMD_DELETE_FILE)
                    {
                        CRP_APP_ERR("invalid cmd:%#x", pReqHead->cmd);
                    }
                    else 
                    {
                        if(g_DcCommunicationProcOpHdl[pReqHead->cmd].nFrameLen != pLsitNode->len)
                        {
                            CRP_APP_ERR("cmd %d invalid len %d, should be %d!", pReqHead->cmd, pLsitNode->len, g_DcCommunicationProcOpHdl[pReqHead->cmd].nFrameLen);
                        }
                        else
                        {
                            if(g_DcCommunicationProcOpHdl[pReqHead->cmd].procFunc)
                            {
                                g_DcCommunicationProcOpHdl[pReqHead->cmd].procFunc(pLsitNode);
                                pDcCommunicationPara->llLegalFrameCnt++;
                                pDcCommunicationPara->llCmdFrameCnt[pReqHead->cmd]++;
                            }
                        }
                    }    
                }
                releaseDcCommunicationListNode(pLsitNode);
            }
        }
    }
    pDcCommunicationPara->runFlag = 0;
    pDcCommunicationPara->exitFlag = 0;
    CRP_APP_INFO("dcCommunicationProcTask exit!!!");
    return 0;
}

int dcCommunicationRecvTask(void)
{
    T_DcCommunicationPara* pDcCommunicationPara = &g_DcCommunicationPara;
    uint8_x pBuf[TEMP_BUF_SIZE];
    unsigned int len;
    int recvLen;
    T_DcCommunicationReqList* pLsitNode;

    prctl(PR_SET_NAME, "dcCommunicationRecvTask");
    while(1)
    {
        if(pDcCommunicationPara->exitFlag == 1)
        {
            break;
        }
        if (pDcCommunicationPara->runFlag == 0)
        {
            mdelay(1);
            continue;
        }
        else
        {
            len = MAX_RCV_SIZE_PER_TIME;
			int i = 0;
            recvLen = multicastRecv(pDcCommunicationPara->reqSocket, (int8_x*)pBuf, len);
            if(recvLen>0)
            {
                DC_REQ_FRAME_COMMON_HEAD* pReqHead = (DC_REQ_FRAME_COMMON_HEAD*)pBuf;
                switchDcReqMsgHeadEndian(pReqHead);
                for(i = 0; i < DC_HEAD_FLAG_LEN; i++)
                {
                    if(pReqHead->head[i] != DC_REQ_HEAD_FLAG)
                    {
                        break;
                    }
                }
				 
                if(i < DC_HEAD_FLAG_LEN)
                {
                    CRP_APP_ERR("invalid head2:%#x, should be %#x", pReqHead->head[i], DC_REQ_HEAD_FLAG);
				 	continue;
                }

			    if(pDcCommunicationPara->nodeNum >= DC_MAX_NODE_NUM)
				{
				    CRP_APP_ERR("The number of cached nodes exceeds the maximum value. maximum value:%d", DC_MAX_NODE_NUM);
					continue;
				}
						
                pLsitNode = createDcCommunicationListNode(pBuf, recvLen);
                if(pLsitNode)
                {
                    dcCommunicationMutexLock();
                    list_add_tail(&pLsitNode->listHead, &pDcCommunicationPara->list.listHead);
					
					//Calculate the number of allocated nodes
					pDcCommunicationPara->nodeNum++;
			        CRP_APP_DBG("pDcCommunicationPara->nodeNum++,pDcCommunicationPara->nodeNum value:%llu",pDcCommunicationPara->nodeNum);
//                  CRP_APP_INFO("add node:%p addr:%p len:%#x.", pNode, pNode->pBuf, pNode->len);
                    dcCommunicationMutexUnlock();
                    pDcCommunicationPara->llRecvFrameCnt ++;
                }
                else
                {
                    CRP_APP_ERR("createDcCommunicationListNode failed!");
                }
            }
            else if(recvLen == 0)
            {
                udelay(1);
            }
            else
            {
                if(pDcCommunicationPara->reqSocket>=0)
                {
                    CloseSocket(pDcCommunicationPara->reqSocket);
                    pDcCommunicationPara->reqSocket = -1;
                    
                }
                CRP_APP_ERR("multicastRecv return %d, close socket and stop recv!!!", recvLen);
                pDcCommunicationPara->runFlag = 0;
            }
        }
    }
    pDcCommunicationPara->runFlag = 0;
    pDcCommunicationPara->exitFlag = 0;
    CRP_APP_INFO("dcCommunicationRcvTask exit!!!");
    return 0;
}

int dcStorageStateReportTask(void)
{
    T_DcCommunicationPara* pDcCommunicationPara = &g_DcCommunicationPara;
    while(1)
    {
        if(getRecordStateAll() > 0)
        {
        	setFcWorkStatus(RECORD_STATUS);
        }
        else if(getNetReplayStateAll() > 0)
        {
        	setFcWorkStatus(REPLAY_STATUS);
        }
        else if(nftServerStateGet() == SRV_DOWNLOAD)
        {
        	setFcWorkStatus(DOWNLOAD_STATUS);
        }
        else if(nftServerStateGet() == SRV_UPLOAD)
        {
        	setFcWorkStatus(UPLOAD_STATUS);
        }
        else if(getFcDeleteStatus())
        {
            setFcWorkStatus(DELETE_STATUS);
        }
        else
        {
            setFcWorkStatus(IDLE_STATUS);
        }
        if(pDcCommunicationPara->runFlag && pDcCommunicationPara->rspSocket>0)
        {
            dcStorageStateReport();
        }
        mdelay(1000);
    }

    return 0;
}

char* getCmdDesc(int cmd)
{
    int i;

    for(i=0; i<sizeof(g_DcCommunicationProcOpHdl)/sizeof(g_DcCommunicationProcOpHdl[0]); i++)
    {
        if(g_DcCommunicationProcOpHdl[i].sMsgCmdId == cmd)
        {
            return g_DcCommunicationProcOpHdl[i].cmdDesc;
        }
    }
    return NULL;
}

void showDcCmdCnt(void)
{
    int i;
    T_DcCommunicationPara* pDcCommunicationPara = &g_DcCommunicationPara;

    CRP_APP_INFO("显控命令统计信息: 总命令数: %llu 合法命令数: %llu", pDcCommunicationPara->llRecvFrameCnt, pDcCommunicationPara->llLegalFrameCnt);
    for(i=0; i<sizeof(g_DcCommunicationProcOpHdl)/sizeof(g_DcCommunicationProcOpHdl[0]); i++)
    {
        if(g_DcCommunicationProcOpHdl[i].procFunc)
        {
            CRP_APP_INFO("%s命令数: %llu" , g_DcCommunicationProcOpHdl[i].cmdDesc, pDcCommunicationPara->llCmdFrameCnt[g_DcCommunicationProcOpHdl[i].sMsgCmdId]);
        }
    }
}

int dcCommunicationInit(void)
{
    int i;
    
    T_DcCommunicationPara* pDcCommunicationPara = &g_DcCommunicationPara;
    int nRet;

    for(i=0; i<sizeof(g_DcCommunicationProcOpHdl)/sizeof(g_DcCommunicationProcOpHdl[0]); i++)
    {
        if(g_DcCommunicationProcOpHdl[i].sMsgCmdId != i)
        {
            memset(&g_DcCommunicationProcOpHdl[i], 0, sizeof(g_DcCommunicationProcOpHdl[i]));
        }
    }
    
    memset(pDcCommunicationPara, 0, sizeof(T_DcCommunicationPara));
    pDcCommunicationPara->rspMutex = mutexCreate();
    if(pDcCommunicationPara->rspMutex == NULL)
    {
        CRP_APP_ERR("create rspMutex failed!");
        return -2;
    }
    pDcCommunicationPara->mutex = mutexCreate();
    if(pDcCommunicationPara->mutex == NULL)
    {
        CRP_APP_ERR("create mutex failed!");
        mutexRelease(pDcCommunicationPara->rspMutex);
        return -3;
    }
    pDcCommunicationPara->workStatusMutex = mutexCreate();
    if(pDcCommunicationPara->workStatusMutex == NULL)
    {
        CRP_APP_ERR("create mutex failed!");
        mutexRelease(pDcCommunicationPara->rspMutex);
        mutexRelease(pDcCommunicationPara->mutex);
        return -3;
    }
    INIT_LIST_HEAD(&pDcCommunicationPara->list.listHead);
    pDcCommunicationPara->reqSocket = multicastCreate(DC_COMMUNICATION_CMD_IP, DC_COMMUNICATION_CMD_PORT, 
                                                      DC_COMMUNICATION_DEV_NAME, DC_COMMUNICATION_RECV_BUF_SIZE, 0);
    if(pDcCommunicationPara->reqSocket < 0)
    {
        CRP_APP_ERR("create reqSocket failed! ret = %d", pDcCommunicationPara->reqSocket);
        mutexRelease(pDcCommunicationPara->rspMutex);
        mutexRelease(pDcCommunicationPara->mutex);
        mutexRelease(pDcCommunicationPara->workStatusMutex);
        return -4;
    }
    else
    {
        CRP_APP_INFO("dcCommunication req multicastCreate success! groupAddr:%s groupPort:%d netDevName:%s socket:%d.",
                     DC_COMMUNICATION_CMD_IP, DC_COMMUNICATION_CMD_PORT, DC_COMMUNICATION_DEV_NAME, pDcCommunicationPara->reqSocket);
    }
    pDcCommunicationPara->rspSocket = multicastCreate(DC_COMMUNICATION_RSP_IP, DC_COMMUNICATION_RSP_PORT, 
                                                      DC_COMMUNICATION_DEV_NAME, DC_COMMUNICATION_SEND_BUF_SIZE, 1);
    if(pDcCommunicationPara->rspSocket < 0)
    {
        CRP_APP_ERR("create rspSocket failed! ret = %d", pDcCommunicationPara->rspSocket);
        mutexRelease(pDcCommunicationPara->rspMutex);
        mutexRelease(pDcCommunicationPara->mutex);
        mutexRelease(pDcCommunicationPara->workStatusMutex);
        CloseSocket(pDcCommunicationPara->reqSocket);
        return -5;
    }
    else
    {
        CRP_APP_ERR("dcCommunication rsp multicastCreate success! groupAddr:%s groupPort:%d netDevName:%s socket:%d.",
                     DC_COMMUNICATION_RSP_IP, DC_COMMUNICATION_RSP_PORT, DC_COMMUNICATION_DEV_NAME, pDcCommunicationPara->rspSocket);
    }
    nRet = threadCreate((THREAD_PROC)dcCommunicationRecvTask, NULL, "dcCommunicationRecvTask", 0, NULL);
    if (nRet < 0)
    {
        mutexRelease(pDcCommunicationPara->rspMutex);
        mutexRelease(pDcCommunicationPara->mutex);
        mutexRelease(pDcCommunicationPara->workStatusMutex);
        CloseSocket(pDcCommunicationPara->reqSocket);
        CloseSocket(pDcCommunicationPara->rspSocket);
        CRP_APP_ERR("err:%s create failed ret=%d","dcCommunicationRcvTask", nRet);
        return -6;
    }

    nRet = threadCreate((THREAD_PROC)dcCommunicationProcTask, NULL, "dcCommunicationProcTask", 0, NULL);
    if (nRet < 0)
    {
        mutexRelease(pDcCommunicationPara->rspMutex);
        mutexRelease(pDcCommunicationPara->mutex);
        mutexRelease(pDcCommunicationPara->workStatusMutex);
        CloseSocket(pDcCommunicationPara->reqSocket);
        CloseSocket(pDcCommunicationPara->rspSocket);
        CRP_APP_ERR("err:%s create failed ret=%d","dcCommunicationProcTask", nRet);
        return -7;
    }
    
    nRet = threadCreate((THREAD_PROC)dcStorageStateReportTask, NULL, "dcStorageStateReportTask", 0, NULL);
    if (nRet < 0)
    {
        mutexRelease(pDcCommunicationPara->rspMutex);
        mutexRelease(pDcCommunicationPara->mutex);
        mutexRelease(pDcCommunicationPara->workStatusMutex);
        CloseSocket(pDcCommunicationPara->reqSocket);
        CloseSocket(pDcCommunicationPara->rspSocket);
        CRP_APP_ERR("err:%s create failed ret=%d","dcCommunicationProcTask", nRet);
        return -8;
    }
    pDcCommunicationPara->runFlag = 1;
    return 0;
}



