CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -pthread -g -O2
LDFLAGS = -pthread -lm

# 目标文件
TARGET = test_comparison
REFACTORED_LIB = libdc_refactored.a

# 源文件
REFACTORED_SOURCES = refactored_dc_communication.c
TEST_SOURCES = test_comparison.c

# 对象文件
REFACTORED_OBJECTS = $(REFACTORED_SOURCES:.c=.o)
TEST_OBJECTS = $(TEST_SOURCES:.c=.o)

# 默认目标
all: $(TARGET) report

# 编译重构后的库
$(REFACTORED_LIB): $(REFACTORED_OBJECTS)
	ar rcs $@ $^

# 编译测试程序
$(TARGET): $(TEST_OBJECTS) $(REFACTORED_LIB)
	$(CC) $(TEST_OBJECTS) -L. -ldc_refactored $(LDFLAGS) -o $@

# 编译对象文件
%.o: %.c
	$(CC) $(CFLAGS) -c $< -o $@

# 运行测试
test: $(TARGET)
	@echo "Running performance comparison tests..."
	@echo "========================================"
	./$(TARGET)

# 生成报告
report: refactoring_report.html
	@echo "Opening refactoring report..."
	@if command -v xdg-open > /dev/null; then \
		xdg-open refactoring_report.html; \
	elif command -v open > /dev/null; then \
		open refactoring_report.html; \
	else \
		echo "Please open refactoring_report.html in your browser"; \
	fi

# 内存检查
memcheck: $(TARGET)
	@echo "Running memory leak detection..."
	@if command -v valgrind > /dev/null; then \
		valgrind --leak-check=full --show-leak-kinds=all ./$(TARGET); \
	else \
		echo "Valgrind not found. Please install valgrind for memory checking."; \
		./$(TARGET); \
	fi

# 性能分析
profile: $(TARGET)
	@echo "Running performance profiling..."
	@if command -v perf > /dev/null; then \
		perf record -g ./$(TARGET); \
		perf report; \
	else \
		echo "perf not found. Running normal test..."; \
		./$(TARGET); \
	fi

# 代码分析
analyze:
	@echo "Analyzing code quality..."
	@echo "Original code analysis:"
	@echo "======================="
	@wc -l 5000rmb.c
	@echo "Functions in original code:"
	@grep -c "^[a-zA-Z_][a-zA-Z0-9_]*.*(" 5000rmb.c || echo "0"
	@echo "Global variables in original code:"
	@grep -c "^[a-zA-Z_][a-zA-Z0-9_]*.*=" 5000rmb.c || echo "0"
	@echo ""
	@echo "Refactored code analysis:"
	@echo "========================"
	@wc -l refactored_dc_communication.h refactored_dc_communication.c
	@echo "Functions in refactored code:"
	@grep -c "^[a-zA-Z_][a-zA-Z0-9_]*.*(" refactored_dc_communication.c || echo "0"
	@echo "Global variables in refactored code:"
	@grep -c "^static.*=" refactored_dc_communication.c || echo "0"

# 代码风格检查
style-check:
	@echo "Checking code style..."
	@echo "======================"
	@echo "Checking for Tab characters:"
	@if grep -P '\t' refactored_dc_communication.c refactored_dc_communication.h; then \
		echo "❌ Found Tab characters! Please use 4 spaces instead."; \
	else \
		echo "✅ No Tab characters found."; \
	fi
	@echo ""
	@echo "Checking for trailing spaces:"
	@if grep -E ' +$$' refactored_dc_communication.c refactored_dc_communication.h; then \
		echo "❌ Found trailing spaces!"; \
	else \
		echo "✅ No trailing spaces found."; \
	fi
	@echo ""
	@echo "Checking ternary operator spacing:"
	@if grep -E '\?[^ ]|[^ ]\?|:[^ ]|[^ ]:' refactored_dc_communication.c refactored_dc_communication.h; then \
		echo "❌ Found ternary operators without proper spacing!"; \
	else \
		echo "✅ Ternary operators properly spaced."; \
	fi
	@echo ""
	@echo "Checking for magic numbers:"
	@if grep -E '[^a-zA-Z_][0-9]{2,}[^a-zA-Z_]' refactored_dc_communication.c | grep -v '//' | head -5; then \
		echo "⚠️  Potential magic numbers found (review needed)."; \
	else \
		echo "✅ No obvious magic numbers found."; \
	fi

# 代码格式化（如果安装了clang-format）
format:
	@echo "Formatting code..."
	@if command -v clang-format > /dev/null; then \
		clang-format -i refactored_dc_communication.c refactored_dc_communication.h test_comparison.c; \
		echo "✅ Code formatted successfully."; \
	else \
		echo "⚠️  clang-format not found. Please install it for automatic formatting."; \
	fi

# 清理
clean:
	rm -f $(TARGET) $(REFACTORED_LIB) *.o *.a
	rm -f perf.data perf.data.old

# 完整测试套件
full-test: clean all test memcheck analyze style-check
	@echo ""
	@echo "Full test suite completed!"
	@echo "=========================="
	@echo "1. Compilation: ✓"
	@echo "2. Performance test: ✓"
	@echo "3. Memory check: ✓"
	@echo "4. Code analysis: ✓"
	@echo "5. Style check: ✓"
	@echo ""
	@echo "Check refactoring_report.html for detailed results."

# 帮助信息
help:
	@echo "Available targets:"
	@echo "=================="
	@echo "all         - Build all targets and generate report"
	@echo "test        - Run performance comparison tests"
	@echo "memcheck    - Run memory leak detection (requires valgrind)"
	@echo "profile     - Run performance profiling (requires perf)"
	@echo "analyze     - Analyze code quality metrics"
	@echo "style-check - Check code style compliance"
	@echo "format      - Format code using clang-format"
	@echo "report      - Open HTML report in browser"
	@echo "clean       - Remove all generated files"
	@echo "full-test   - Run complete test suite"
	@echo "help        - Show this help message"

# 安装依赖（Ubuntu/Debian）
install-deps:
	@echo "Installing dependencies..."
	@if command -v apt-get > /dev/null; then \
		sudo apt-get update; \
		sudo apt-get install -y build-essential valgrind linux-tools-common; \
	elif command -v yum > /dev/null; then \
		sudo yum install -y gcc make valgrind perf; \
	elif command -v brew > /dev/null; then \
		brew install valgrind; \
	else \
		echo "Please install gcc, make, valgrind manually"; \
	fi

# 创建测试数据
test-data:
	@echo "Generating test data..."
	@mkdir -p test_data
	@for i in {1..100}; do \
		echo "Test data file $$i" > test_data/file_$$i.dat; \
	done
	@echo "Test data generated in test_data/"

# 基准测试
benchmark: $(TARGET)
	@echo "Running benchmark tests..."
	@echo "=========================="
	@for i in 1000 5000 10000 20000; do \
		echo "Testing with $$i iterations:"; \
		echo "$$i" | ./$(TARGET); \
		echo ""; \
	done

.PHONY: all test report memcheck profile analyze clean full-test help install-deps test-data benchmark
