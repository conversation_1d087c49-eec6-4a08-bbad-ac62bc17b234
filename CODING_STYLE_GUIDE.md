# 代码风格指南

## 概述

本文档定义了DC通信系统项目的代码风格规范，旨在提高代码的可读性、一致性和可维护性。

## 1. 基本格式规范

### 1.1 缩进和空格

- **使用4个空格进行缩进，禁止使用Tab键**
- **不要混用Tab和空格**
- 行尾不要有多余的空格

```c
// ✅ 正确
if (condition) {
    function_call();
    another_function();
}

// ❌ 错误 - 使用Tab或混用
if (condition) {
	function_call();    // Tab缩进
    another_function(); // 空格缩进
}
```

### 1.2 运算符空格

- **三元运算符前后必须有空格**
- **二元运算符前后必须有空格**
- **一元运算符后不加空格**

```c
// ✅ 正确
result = (condition) ? value1 : value2;
sum = a + b;
ptr = &variable;
value = *ptr;

// ❌ 错误
result = (condition)?value1:value2;  // 缺少空格
sum = a+b;                          // 缺少空格
ptr = & variable;                   // 多余空格
```

### 1.3 括号和大括号

- **函数定义的大括号另起一行**
- **控制语句的大括号跟在同一行**
- **函数调用的括号前不加空格**

```c
// ✅ 正确 - 函数定义
int function_name(int param)
{
    // 函数体
}

// ✅ 正确 - 控制语句
if (condition) {
    // 语句块
} else {
    // 语句块
}

// ✅ 正确 - 函数调用
result = function_call(param1, param2);

// ❌ 错误
if(condition){  // 缺少空格
    // 语句块
}

result = function_call (param1, param2);  // 多余空格
```

## 2. 命名规范

### 2.1 函数命名

- **使用小写字母和下划线**
- **动词开头，描述功能**
- **模块前缀 + 功能描述**

```c
// ✅ 正确
dc_device_set_status();
memory_pool_create();
message_queue_push();

// ❌ 错误
dcDeviceSetStatus();    // 驼峰命名
SetDeviceStatus();      // 大写开头
device_set();           // 缺少模块前缀
```

### 2.2 变量命名

- **使用小写字母和下划线**
- **名词性，描述内容**
- **避免缩写和单字母变量**

```c
// ✅ 正确
int device_count;
char *error_message;
dc_message_t *current_message;

// ❌ 错误
int devCnt;         // 缩写
char *errMsg;       // 缩写
int i, j, k;        // 单字母（除循环变量外）
```

### 2.3 常量和宏

- **全大写字母和下划线**
- **模块前缀**

```c
// ✅ 正确
#define DC_MAX_MSG_SIZE     4096
#define DC_ERROR_TIMEOUT    -6
#define REC_MD_NUM          2

// ❌ 错误
#define maxMsgSize          4096  // 驼峰命名
#define ERROR_TIMEOUT       -6    // 缺少模块前缀
```

### 2.4 类型定义

- **使用小写字母和下划线**
- **以_t结尾**

```c
// ✅ 正确
typedef struct dc_message dc_message_t;
typedef enum device_status device_status_t;

// ❌ 错误
typedef struct DcMessage DcMessage;     // 驼峰命名
typedef enum DeviceStatus DeviceStatus; // 没有_t后缀
```

## 3. 函数设计规范

### 3.1 函数作用域

- **同一文件内的辅助函数使用static声明**
- **对外接口函数不使用static**

```c
// ✅ 正确
static bool is_valid_device_id(uint32_t device_id);
static void cleanup_resources(void);

// 对外接口
dc_error_code_t dc_device_set_status(dc_device_manager_t *mgr, 
                                     uint32_t device_id, 
                                     device_status_t status);

// ❌ 错误 - 辅助函数没有static
bool is_valid_device_id(uint32_t device_id);  // 应该是static
```

### 3.2 参数验证

- **所有公共函数都要进行参数验证**
- **返回统一的错误码**

```c
// ✅ 正确
dc_error_code_t dc_device_set_status(dc_device_manager_t *mgr, 
                                     uint32_t device_id, 
                                     device_status_t status) {
    if (!mgr || !is_valid_device_id(device_id)) {
        return DC_ERROR_INVALID_PARAM;
    }
    
    // 实际逻辑
    return DC_SUCCESS;
}

// ❌ 错误 - 缺少参数验证
dc_error_code_t dc_device_set_status(dc_device_manager_t *mgr, 
                                     uint32_t device_id, 
                                     device_status_t status) {
    // 直接使用参数，可能导致崩溃
    mgr->devices[device_id].status = status;
    return DC_SUCCESS;
}
```

### 3.3 错误处理

- **使用统一的错误码枚举**
- **每个分支都要有明确的返回值**

```c
// ✅ 正确
dc_error_code_t function_with_error_handling(void) {
    if (condition1) {
        return DC_ERROR_INVALID_PARAM;
    }
    
    if (condition2) {
        return DC_ERROR_MEMORY_ALLOC;
    }
    
    return DC_SUCCESS;
}

// ❌ 错误 - 不一致的错误处理
int function_with_bad_error_handling(void) {
    if (condition1) {
        return -1;      // 魔数
    }
    
    if (condition2) {
        return NULL;    // 类型不匹配
    }
    
    return 0;
}
```

## 4. 内存管理规范

### 4.1 资源分配和释放

- **每个malloc都要有对应的free**
- **使用RAII模式**
- **检查分配是否成功**

```c
// ✅ 正确
dc_message_t* create_message(void) {
    dc_message_t *msg = calloc(1, sizeof(dc_message_t));
    if (!msg) {
        return NULL;
    }
    
    msg->data = malloc(DATA_SIZE);
    if (!msg->data) {
        free(msg);  // 清理已分配的内存
        return NULL;
    }
    
    return msg;
}

void destroy_message(dc_message_t *msg) {
    if (!msg) {
        return;
    }
    
    if (msg->data) {
        free(msg->data);
        msg->data = NULL;
    }
    
    free(msg);
}

// ❌ 错误 - 内存泄漏
dc_message_t* create_message_bad(void) {
    dc_message_t *msg = malloc(sizeof(dc_message_t));
    msg->data = malloc(DATA_SIZE);  // 没有检查malloc是否成功
    return msg;                     // 如果data分配失败，msg泄漏
}
```

## 5. 注释规范

### 5.1 函数注释

```c
/**
 * @brief 设置设备状态
 * 
 * @param mgr 设备管理器指针
 * @param device_id 设备ID (0 到 REC_MD_NUM-1)
 * @param status 新的设备状态
 * 
 * @return DC_SUCCESS 成功
 * @return DC_ERROR_INVALID_PARAM 参数无效
 */
dc_error_code_t dc_device_set_status(dc_device_manager_t *mgr, 
                                     uint32_t device_id, 
                                     device_status_t status);
```

### 5.2 代码注释

```c
// ✅ 正确 - 解释为什么这样做
// 使用内存池减少频繁的malloc/free开销
void* block = memory_pool_alloc(pool);

// ✅ 正确 - 解释复杂的逻辑
// 检查时间范围：开始时间必须小于结束时间，
// 且结束时间不能超过24小时前
if (!is_time_range_valid(start_time, end_time)) {
    return DC_ERROR_INVALID_PARAM;
}

// ❌ 错误 - 重复代码内容
// 设置设备状态为IDLE
device->status = DEVICE_STATUS_IDLE;
```

## 6. 代码检查工具

### 6.1 推荐工具

- **clang-format**: 自动格式化代码
- **cppcheck**: 静态代码分析
- **valgrind**: 内存检查
- **splint**: 代码质量检查

### 6.2 配置文件

创建`.clang-format`文件：

```yaml
BasedOnStyle: LLVM
IndentWidth: 4
UseTab: Never
BreakBeforeBraces: Linux
SpaceBeforeParens: ControlStatements
SpaceInEmptyParentheses: false
SpacesInParentheses: false
SpacesInSquareBrackets: false
SpaceAfterCStyleCast: true
SpaceBeforeAssignmentOperators: true
```

## 7. 代码审查清单

在提交代码前，请检查以下项目：

- [ ] 使用4个空格缩进，没有Tab字符
- [ ] 三元运算符和二元运算符前后有空格
- [ ] 函数命名使用小写字母和下划线
- [ ] 辅助函数使用static声明
- [ ] 所有公共函数都有参数验证
- [ ] 使用统一的错误码
- [ ] 每个malloc都有对应的free
- [ ] 函数有适当的注释
- [ ] 没有魔数，使用命名常量
- [ ] 代码通过静态分析工具检查

## 8. 示例对比

### 原代码风格问题

```c
// ❌ 原代码的问题
T_DcCommunicationReqList* createDcCommunicationListNode(uint8_x* pBuf, uint32_x len)
{
    T_DcCommunicationReqList* pLsitNode = NULL;  // 拼写错误

    pLsitNode = (T_DcCommunicationReqList*)malloc(sizeof(T_DcCommunicationReqList));
    if(pLsitNode!=NULL)  // 缺少空格
    {
        memcpy(pLsitNode->pBuf, pBuf, len);  // 没有边界检查
        pLsitNode->len = len;
    }
    else
    {
        CRP_APP_ERR("createDcCommunicationListNode failed! malloc failed!");
    }
    return pLsitNode;
}

int getKeyDataFlagState(void)
{
    return (g_keyDataMsg.g_keyDataFlag)?1:0;  // 三元运算符缺少空格
}
```

### 重构后的规范代码

```c
// ✅ 重构后的规范代码
static bool is_valid_buffer_size(size_t size) {
    return size > 0 && size <= DC_MAX_MSG_SIZE;
}

dc_error_code_t dc_message_create(dc_message_t **msg, uint16_t cmd, 
                                 const void *data, uint32_t data_len) {
    if (!msg || !is_valid_buffer_size(data_len)) {
        return DC_ERROR_INVALID_PARAM;
    }
    
    dc_message_t *new_msg = calloc(1, sizeof(dc_message_t));
    if (!new_msg) {
        return DC_ERROR_MEMORY_ALLOC;
    }
    
    if (data && data_len > 0) {
        new_msg->data = malloc(data_len);
        if (!new_msg->data) {
            free(new_msg);
            return DC_ERROR_MEMORY_ALLOC;
        }
        memcpy(new_msg->data, data, data_len);
    }
    
    *msg = new_msg;
    return DC_SUCCESS;
}

bool dc_get_key_data_flag_state(void) {
    return g_key_data_flag ? true : false;
}
```

遵循这些规范可以显著提高代码质量，减少bug，提高可维护性。
