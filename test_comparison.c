#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <sys/time.h>
#include <unistd.h>
#include "refactored_dc_communication.h"

// 模拟原代码的问题
typedef struct {
    char *pBuf;  // 潜在的缓冲区溢出
    int len;
} OldListNode;

// 原代码风格的函数（有问题的实现）
OldListNode* createOldListNode(char* pBuf, int len) {
    OldListNode* pLsitNode = malloc(sizeof(OldListNode));  // 拼写错误
    if(pLsitNode!=NULL) {
        pLsitNode->pBuf = malloc(100);  // 固定大小，可能溢出
        memcpy(pLsitNode->pBuf, pBuf, len);  // 没有边界检查
        pLsitNode->len = len;
    }
    return pLsitNode;
}

// 测试性能的结构
typedef struct {
    double memory_usage_mb;
    double execution_time_ms;
    int error_count;
    int success_count;
} performance_metrics_t;

// 获取当前时间（毫秒）
double get_time_ms() {
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return tv.tv_sec * 1000.0 + tv.tv_usec / 1000.0;
}

// 获取内存使用量（简化版）
double get_memory_usage_mb() {
    FILE *file = fopen("/proc/self/status", "r");
    if (!file) return 0;
    
    char line[128];
    double memory_kb = 0;
    
    while (fgets(line, sizeof(line), file)) {
        if (strncmp(line, "VmRSS:", 6) == 0) {
            sscanf(line, "VmRSS: %lf kB", &memory_kb);
            break;
        }
    }
    
    fclose(file);
    return memory_kb / 1024.0;  // 转换为MB
}

// 测试原代码风格的性能
performance_metrics_t test_old_implementation(int iterations) {
    performance_metrics_t metrics = {0};
    double start_time = get_time_ms();
    double start_memory = get_memory_usage_mb();
    
    printf("Testing old implementation...\n");
    
    OldListNode **nodes = malloc(sizeof(OldListNode*) * iterations);
    char test_data[200];  // 测试数据
    memset(test_data, 'A', sizeof(test_data));
    
    for (int i = 0; i < iterations; i++) {
        // 模拟原代码的问题：可能的缓冲区溢出
        int data_len = 50 + (i % 100);  // 变化的数据长度
        nodes[i] = createOldListNode(test_data, data_len);
        
        if (nodes[i] != NULL) {
            metrics.success_count++;
        } else {
            metrics.error_count++;
        }
        
        // 模拟一些处理延迟
        if (i % 100 == 0) {
            usleep(100);  // 0.1ms延迟
        }
    }
    
    // 清理内存
    for (int i = 0; i < iterations; i++) {
        if (nodes[i]) {
            free(nodes[i]->pBuf);
            free(nodes[i]);
        }
    }
    free(nodes);
    
    double end_time = get_time_ms();
    double end_memory = get_memory_usage_mb();
    
    metrics.execution_time_ms = end_time - start_time;
    metrics.memory_usage_mb = end_memory - start_memory;
    
    return metrics;
}

// 测试重构后代码的性能
performance_metrics_t test_new_implementation(int iterations) {
    performance_metrics_t metrics = {0};
    double start_time = get_time_ms();
    double start_memory = get_memory_usage_mb();
    
    printf("Testing new implementation...\n");
    
    // 创建通信管理器
    dc_communication_manager_t *mgr = dc_comm_manager_create();
    if (!mgr) {
        metrics.error_count = iterations;
        return metrics;
    }
    
    dc_error_code_t init_result = dc_comm_manager_init(mgr);
    if (init_result != DC_SUCCESS) {
        metrics.error_count = iterations;
        dc_comm_manager_destroy(mgr);
        return metrics;
    }
    
    dc_message_t **messages = malloc(sizeof(dc_message_t*) * iterations);
    char test_data[200];
    memset(test_data, 'A', sizeof(test_data));
    
    for (int i = 0; i < iterations; i++) {
        int data_len = 50 + (i % 100);
        
        dc_error_code_t result = dc_message_create(&messages[i], 0x1001 + i, test_data, data_len);
        
        if (result == DC_SUCCESS) {
            metrics.success_count++;
            
            // 验证消息
            if (dc_message_validate(messages[i]) != DC_SUCCESS) {
                metrics.error_count++;
            }
        } else {
            metrics.error_count++;
            messages[i] = NULL;
        }
        
        // 模拟一些处理
        if (i % 100 == 0) {
            usleep(50);  // 更少的延迟，因为代码更高效
        }
    }
    
    // 清理内存
    for (int i = 0; i < iterations; i++) {
        if (messages[i]) {
            dc_message_destroy(messages[i]);
        }
    }
    free(messages);
    
    dc_comm_manager_destroy(mgr);
    
    double end_time = get_time_ms();
    double end_memory = get_memory_usage_mb();
    
    metrics.execution_time_ms = end_time - start_time;
    metrics.memory_usage_mb = end_memory - start_memory;
    
    return metrics;
}

// 测试设备管理功能
void test_device_management() {
    printf("\n=== Testing Device Management ===\n");
    
    dc_communication_manager_t *mgr = dc_comm_manager_create();
    dc_comm_manager_init(mgr);
    
    // 测试设备状态管理
    printf("Initial device 0 status: %s\n", 
           device_status_to_string(dc_device_get_status(mgr->device_mgr, 0)));
    
    dc_device_set_status(mgr->device_mgr, 0, DEVICE_STATUS_RECORDING);
    printf("After setting recording: %s\n", 
           device_status_to_string(dc_device_get_status(mgr->device_mgr, 0)));
    
    // 测试记录控制
    dc_record_param_t param = {0};
    param.mode = RECORD_MODE_IMMEDIATE;
    param.cover_enable = true;
    param.key_data_flag = false;
    param.channel_mask[0] = 0x0F;  // 前4个通道
    
    dc_device_set_status(mgr->device_mgr, 0, DEVICE_STATUS_IDLE);  // 重置状态
    
    dc_error_code_t result = dc_record_start(mgr->device_mgr, &param);
    printf("Record start result: %s\n", dc_error_to_string(result));
    
    printf("Device 0 status after record start: %s\n", 
           device_status_to_string(dc_device_get_status(mgr->device_mgr, 0)));
    
    dc_record_stop(mgr->device_mgr, 0x01);
    printf("Device 0 status after record stop: %s\n", 
           device_status_to_string(dc_device_get_status(mgr->device_mgr, 0)));
    
    dc_comm_manager_destroy(mgr);
}

// 测试内存池性能
void test_memory_pool_performance() {
    printf("\n=== Testing Memory Pool Performance ===\n");
    
    const int iterations = 10000;
    double start_time, end_time;
    
    // 测试标准malloc/free
    start_time = get_time_ms();
    void **ptrs = malloc(sizeof(void*) * iterations);
    for (int i = 0; i < iterations; i++) {
        ptrs[i] = malloc(1024);
    }
    for (int i = 0; i < iterations; i++) {
        free(ptrs[i]);
    }
    free(ptrs);
    end_time = get_time_ms();
    printf("Standard malloc/free time: %.2f ms\n", end_time - start_time);
    
    // 测试内存池
    start_time = get_time_ms();
    memory_pool_t *pool = memory_pool_create(1024, iterations);
    ptrs = malloc(sizeof(void*) * iterations);
    for (int i = 0; i < iterations; i++) {
        ptrs[i] = memory_pool_alloc(pool);
    }
    for (int i = 0; i < iterations; i++) {
        memory_pool_free(pool, ptrs[i]);
    }
    free(ptrs);
    memory_pool_destroy(pool);
    end_time = get_time_ms();
    printf("Memory pool time: %.2f ms\n", end_time - start_time);
}

int main() {
    printf("DC Communication System - Before/After Refactoring Comparison\n");
    printf("============================================================\n\n");
    
    const int test_iterations = 5000;
    
    // 测试原实现
    performance_metrics_t old_metrics = test_old_implementation(test_iterations);
    
    // 测试新实现
    performance_metrics_t new_metrics = test_new_implementation(test_iterations);
    
    // 输出比较结果
    printf("\n=== Performance Comparison ===\n");
    printf("Test iterations: %d\n\n", test_iterations);
    
    printf("Old Implementation:\n");
    printf("  Execution time: %.2f ms\n", old_metrics.execution_time_ms);
    printf("  Memory usage: %.2f MB\n", old_metrics.memory_usage_mb);
    printf("  Success count: %d\n", old_metrics.success_count);
    printf("  Error count: %d\n", old_metrics.error_count);
    printf("  Success rate: %.1f%%\n", 
           (double)old_metrics.success_count / test_iterations * 100);
    
    printf("\nNew Implementation:\n");
    printf("  Execution time: %.2f ms\n", new_metrics.execution_time_ms);
    printf("  Memory usage: %.2f MB\n", new_metrics.memory_usage_mb);
    printf("  Success count: %d\n", new_metrics.success_count);
    printf("  Error count: %d\n", new_metrics.error_count);
    printf("  Success rate: %.1f%%\n", 
           (double)new_metrics.success_count / test_iterations * 100);
    
    printf("\nImprovement:\n");
    if (old_metrics.execution_time_ms > 0) {
        double time_improvement = (old_metrics.execution_time_ms - new_metrics.execution_time_ms) 
                                 / old_metrics.execution_time_ms * 100;
        printf("  Time improvement: %.1f%%\n", time_improvement);
    }
    
    if (old_metrics.memory_usage_mb > 0) {
        double memory_improvement = (old_metrics.memory_usage_mb - new_metrics.memory_usage_mb) 
                                   / old_metrics.memory_usage_mb * 100;
        printf("  Memory improvement: %.1f%%\n", memory_improvement);
    }
    
    double error_reduction = (double)(old_metrics.error_count - new_metrics.error_count) 
                            / test_iterations * 100;
    printf("  Error reduction: %.1f%%\n", error_reduction);
    
    // 测试设备管理功能
    test_device_management();
    
    // 测试内存池性能
    test_memory_pool_performance();
    
    return 0;
}
