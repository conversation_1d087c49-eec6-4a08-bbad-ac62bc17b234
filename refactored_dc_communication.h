#ifndef REFACTORED_DC_COMMUNICATION_H
#define REFACTORED_DC_COMMUNICATION_H

#include <stdint.h>
#include <stdbool.h>
#include <pthread.h>

// 常量定义
#define DC_MAX_MSG_SIZE         4096
#define DC_HEAD_FLAG_LEN        7
#define DC_CHN_NUM              16
#define REC_MD_NUM              2
#define MAX_RECORD_CHANNEL_NUM  12
#define DC_PACKET_MAX_FILE_NUM  20

// 错误码定义
typedef enum {
    DC_SUCCESS = 0,
    DC_ERROR_INVALID_PARAM = -1,
    DC_ERROR_MEMORY_ALLOC = -2,
    DC_ERROR_CHECKSUM = -3,
    DC_ERROR_DEVICE_BUSY = -4,
    DC_ERROR_DEVICE_OFFLINE = -5,
    DC_ERROR_TIMEOUT = -6
} dc_error_code_t;

// 设备状态枚举
typedef enum {
    DEVICE_STATUS_IDLE = 1,
    DEVICE_STATUS_RECORDING = 2,
    DEVICE_STATUS_REPLAYING = 3,
    DEVICE_STATUS_DOWNLOADING = 4,
    DEVICE_STATUS_UPLOADING = 5,
    DEVICE_STATUS_DELETING = 6,
    DEVICE_STATUS_INVALID = 0xFF
} device_status_t;

// 记录模式枚举
typedef enum {
    RECORD_MODE_POWER_ON = 0,
    RECORD_MODE_IMMEDIATE = 1,
    RECORD_MODE_TIMED = 2
} record_mode_t;

// 前向声明
typedef struct dc_message dc_message_t;
typedef struct dc_communication_manager dc_communication_manager_t;
typedef struct dc_command_handler dc_command_handler_t;
typedef struct dc_device_manager dc_device_manager_t;

// 消息结构
typedef struct dc_message {
    uint16_t cmd;
    uint16_t len;
    uint32_t timestamp;
    uint32_t device_id;
    uint8_t *data;
    uint32_t data_len;
    uint8_t checksum;
} dc_message_t;

// 设备信息结构
typedef struct dc_device_info {
    uint32_t device_id;
    device_status_t status;
    bool is_online;
    uint64_t total_capacity;
    uint64_t free_capacity;
    uint32_t channel_mask;
    pthread_mutex_t mutex;
} dc_device_info_t;

// 记录参数结构
typedef struct dc_record_param {
    record_mode_t mode;
    bool cover_enable;
    bool key_data_flag;
    uint32_t channel_mask[REC_MD_NUM];
    uint64_t start_time_ms;
    uint64_t end_time_ms;
    device_status_t status[REC_MD_NUM];
} dc_record_param_t;

// 文件信息结构
typedef struct dc_file_info {
    char path[256];
    uint64_t size;
    uint64_t create_time;
    uint32_t device_id;
    uint16_t channel;
    bool key_data_flag;
    bool downloaded_flag;
} dc_file_info_t;

// 内存池结构
typedef struct memory_pool {
    void **free_blocks;
    size_t block_size;
    size_t total_blocks;
    size_t free_count;
    pthread_mutex_t mutex;
} memory_pool_t;

// 消息队列结构
typedef struct message_queue {
    dc_message_t **messages;
    size_t capacity;
    size_t head;
    size_t tail;
    size_t count;
    pthread_mutex_t mutex;
    pthread_cond_t not_empty;
    pthread_cond_t not_full;
} message_queue_t;

// 命令处理函数类型
typedef dc_error_code_t (*command_handler_func_t)(const dc_message_t *request, dc_message_t *response);

// 命令处理器结构
typedef struct dc_command_handler {
    uint16_t cmd_id;
    command_handler_func_t handler;
    const char *description;
    size_t expected_size;
} dc_command_handler_t;

// 设备管理器结构
typedef struct dc_device_manager {
    dc_device_info_t devices[REC_MD_NUM];
    dc_record_param_t record_param;
    dc_record_param_t power_on_param;
    pthread_mutex_t param_mutex;
    bool debug_enabled;
} dc_device_manager_t;

// 网络通信结构
typedef struct dc_network_comm {
    int req_socket;
    int rsp_socket;
    pthread_mutex_t send_mutex;
    bool is_running;
    pthread_t recv_thread;
    pthread_t proc_thread;
} dc_network_comm_t;

// 统计信息结构
typedef struct dc_statistics {
    uint64_t total_frames;
    uint64_t valid_frames;
    uint64_t cmd_counts[256];
    pthread_mutex_t mutex;
} dc_statistics_t;

// 主通信管理器结构
typedef struct dc_communication_manager {
    dc_device_manager_t *device_mgr;
    dc_network_comm_t *network;
    message_queue_t *msg_queue;
    memory_pool_t *mem_pool;
    dc_statistics_t *stats;
    dc_command_handler_t *handlers;
    size_t handler_count;
    bool is_initialized;
    pthread_mutex_t global_mutex;
} dc_communication_manager_t;

// API函数声明
dc_communication_manager_t* dc_comm_manager_create(void);
dc_error_code_t dc_comm_manager_init(dc_communication_manager_t *mgr);
dc_error_code_t dc_comm_manager_start(dc_communication_manager_t *mgr);
dc_error_code_t dc_comm_manager_stop(dc_communication_manager_t *mgr);
void dc_comm_manager_destroy(dc_communication_manager_t *mgr);

// 消息处理API
dc_error_code_t dc_message_create(dc_message_t **msg, uint16_t cmd, const void *data, uint32_t data_len);
void dc_message_destroy(dc_message_t *msg);
dc_error_code_t dc_message_validate(const dc_message_t *msg);

// 设备管理API
dc_error_code_t dc_device_set_status(dc_device_manager_t *mgr, uint32_t device_id, device_status_t status);
device_status_t dc_device_get_status(dc_device_manager_t *mgr, uint32_t device_id);
bool dc_device_is_online(dc_device_manager_t *mgr, uint32_t device_id);

// 记录控制API
dc_error_code_t dc_record_start(dc_device_manager_t *mgr, const dc_record_param_t *param);
dc_error_code_t dc_record_stop(dc_device_manager_t *mgr, uint32_t device_mask);
dc_error_code_t dc_replay_start(dc_device_manager_t *mgr, const char *file_paths[], uint32_t device_mask);
dc_error_code_t dc_replay_stop(dc_device_manager_t *mgr, uint32_t device_mask);

// 文件管理API
dc_error_code_t dc_file_list_get(dc_device_manager_t *mgr, dc_file_info_t **files, size_t *count, 
                                uint64_t start_time, uint64_t end_time, uint32_t channel_mask);
dc_error_code_t dc_file_delete(dc_device_manager_t *mgr, const char *file_path, uint32_t device_id);

// 内存池API
memory_pool_t* memory_pool_create(size_t block_size, size_t block_count);
void* memory_pool_alloc(memory_pool_t *pool);
void memory_pool_free(memory_pool_t *pool, void *ptr);
void memory_pool_destroy(memory_pool_t *pool);

// 消息队列API
message_queue_t* message_queue_create(size_t capacity);
dc_error_code_t message_queue_push(message_queue_t *queue, dc_message_t *msg);
dc_error_code_t message_queue_pop(message_queue_t *queue, dc_message_t **msg, int timeout_ms);
void message_queue_destroy(message_queue_t *queue);

// 工具函数
uint8_t calculate_checksum(const void *data, size_t len);
dc_error_code_t validate_time_range(uint64_t start_time, uint64_t end_time);
const char* dc_error_to_string(dc_error_code_t error);
const char* device_status_to_string(device_status_t status);

#endif // REFACTORED_DC_COMMUNICATION_H
