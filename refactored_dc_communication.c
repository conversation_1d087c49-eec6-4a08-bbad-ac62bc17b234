#include "refactored_dc_communication.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <time.h>
#include <unistd.h>

// 全局管理器实例
static dc_communication_manager_t *g_dc_manager = NULL;

// 静态辅助函数声明
static bool is_valid_device_id(uint32_t device_id);
static bool is_time_range_valid(uint64_t start_time, uint64_t end_time);
static void cleanup_memory_pool_blocks(memory_pool_t *pool);

// 错误码到字符串的映射
const char* dc_error_to_string(dc_error_code_t error) {
    switch (error) {
        case DC_SUCCESS:
            return "Success";
        case DC_ERROR_INVALID_PARAM:
            return "Invalid parameter";
        case DC_ERROR_MEMORY_ALLOC:
            return "Memory allocation failed";
        case DC_ERROR_CHECKSUM:
            return "Checksum error";
        case DC_ERROR_DEVICE_BUSY:
            return "Device busy";
        case DC_ERROR_DEVICE_OFFLINE:
            return "Device offline";
        case DC_ERROR_TIMEOUT:
            return "Timeout";
        default:
            return "Unknown error";
    }
}

// 设备状态到字符串的映射
const char* device_status_to_string(device_status_t status) {
    switch (status) {
        case DEVICE_STATUS_IDLE:
            return "Idle";
        case DEVICE_STATUS_RECORDING:
            return "Recording";
        case DEVICE_STATUS_REPLAYING:
            return "Replaying";
        case DEVICE_STATUS_DOWNLOADING:
            return "Downloading";
        case DEVICE_STATUS_UPLOADING:
            return "Uploading";
        case DEVICE_STATUS_DELETING:
            return "Deleting";
        case DEVICE_STATUS_INVALID:
            return "Invalid";
        default:
            return "Unknown";
    }
}

// 计算校验和
uint8_t calculate_checksum(const void *data, size_t len) {
    if (!data || len == 0) {
        return 0;
    }

    const uint8_t *bytes = (const uint8_t *)data;
    uint8_t checksum = 0;

    for (size_t i = 0; i < len; i++) {
        checksum ^= bytes[i];
    }

    return checksum;
}

// 验证时间范围
dc_error_code_t validate_time_range(uint64_t start_time, uint64_t end_time) {
    if (!is_time_range_valid(start_time, end_time)) {
        return DC_ERROR_INVALID_PARAM;
    }

    return DC_SUCCESS;
}

// 静态辅助函数实现
static bool is_valid_device_id(uint32_t device_id) {
    return device_id < REC_MD_NUM;
}

static bool is_time_range_valid(uint64_t start_time, uint64_t end_time) {
    if (start_time >= end_time) {
        return false;
    }

    uint64_t current_time = (uint64_t)time(NULL) * 1000;
    // 超过24小时前的时间认为无效
    if (end_time <= current_time - (24 * 60 * 60 * 1000)) {
        return false;
    }

    return true;
}

static void cleanup_memory_pool_blocks(memory_pool_t *pool) {
    if (!pool || !pool->free_blocks) {
        return;
    }

    for (size_t i = 0; i < pool->total_blocks; i++) {
        if (pool->free_blocks[i]) {
            free(pool->free_blocks[i]);
            pool->free_blocks[i] = NULL;
        }
    }
}

// 内存池实现
memory_pool_t* memory_pool_create(size_t block_size, size_t block_count) {
    if (block_size == 0 || block_count == 0) {
        return NULL;
    }

    memory_pool_t *pool = calloc(1, sizeof(memory_pool_t));
    if (!pool) {
        return NULL;
    }

    pool->block_size = block_size;
    pool->total_blocks = block_count;
    pool->free_count = block_count;

    // 分配内存块数组
    pool->free_blocks = malloc(sizeof(void*) * block_count);
    if (!pool->free_blocks) {
        free(pool);
        return NULL;
    }

    // 分配实际的内存块
    for (size_t i = 0; i < block_count; i++) {
        pool->free_blocks[i] = malloc(block_size);
        if (!pool->free_blocks[i]) {
            // 清理已分配的内存
            cleanup_memory_pool_blocks(pool);
            free(pool->free_blocks);
            free(pool);
            return NULL;
        }
    }

    if (pthread_mutex_init(&pool->mutex, NULL) != 0) {
        cleanup_memory_pool_blocks(pool);
        free(pool->free_blocks);
        free(pool);
        return NULL;
    }

    return pool;
}

void* memory_pool_alloc(memory_pool_t *pool) {
    if (!pool) {
        return NULL;
    }

    pthread_mutex_lock(&pool->mutex);

    void *block = NULL;
    if (pool->free_count > 0) {
        block = pool->free_blocks[--pool->free_count];
    }

    pthread_mutex_unlock(&pool->mutex);

    return block;
}

void memory_pool_free(memory_pool_t *pool, void *ptr) {
    if (!pool || !ptr) {
        return;
    }

    pthread_mutex_lock(&pool->mutex);

    if (pool->free_count < pool->total_blocks) {
        pool->free_blocks[pool->free_count++] = ptr;
    }

    pthread_mutex_unlock(&pool->mutex);
}

void memory_pool_destroy(memory_pool_t *pool) {
    if (!pool) {
        return;
    }

    pthread_mutex_destroy(&pool->mutex);

    cleanup_memory_pool_blocks(pool);

    free(pool->free_blocks);
    free(pool);
}

// 消息队列实现
message_queue_t* message_queue_create(size_t capacity) {
    if (capacity == 0) return NULL;

    message_queue_t *queue = calloc(1, sizeof(message_queue_t));
    if (!queue) return NULL;

    queue->messages = malloc(sizeof(dc_message_t*) * capacity);
    if (!queue->messages) {
        free(queue);
        return NULL;
    }

    queue->capacity = capacity;
    queue->head = 0;
    queue->tail = 0;
    queue->count = 0;

    if (pthread_mutex_init(&queue->mutex, NULL) != 0 ||
        pthread_cond_init(&queue->not_empty, NULL) != 0 ||
        pthread_cond_init(&queue->not_full, NULL) != 0) {
        free(queue->messages);
        free(queue);
        return NULL;
    }

    return queue;
}

dc_error_code_t message_queue_push(message_queue_t *queue, dc_message_t *msg) {
    if (!queue || !msg) return DC_ERROR_INVALID_PARAM;

    pthread_mutex_lock(&queue->mutex);

    while (queue->count >= queue->capacity) {
        pthread_cond_wait(&queue->not_full, &queue->mutex);
    }

    queue->messages[queue->tail] = msg;
    queue->tail = (queue->tail + 1) % queue->capacity;
    queue->count++;

    pthread_cond_signal(&queue->not_empty);
    pthread_mutex_unlock(&queue->mutex);

    return DC_SUCCESS;
}

dc_error_code_t message_queue_pop(message_queue_t *queue, dc_message_t **msg, int timeout_ms) {
    if (!queue || !msg) return DC_ERROR_INVALID_PARAM;

    pthread_mutex_lock(&queue->mutex);

    if (timeout_ms > 0) {
        struct timespec ts;
        clock_gettime(CLOCK_REALTIME, &ts);
        ts.tv_sec += timeout_ms / 1000;
        ts.tv_nsec += (timeout_ms % 1000) * 1000000;

        while (queue->count == 0) {
            if (pthread_cond_timedwait(&queue->not_empty, &queue->mutex, &ts) != 0) {
                pthread_mutex_unlock(&queue->mutex);
                return DC_ERROR_TIMEOUT;
            }
        }
    } else {
        while (queue->count == 0) {
            pthread_cond_wait(&queue->not_empty, &queue->mutex);
        }
    }

    *msg = queue->messages[queue->head];
    queue->head = (queue->head + 1) % queue->capacity;
    queue->count--;

    pthread_cond_signal(&queue->not_full);
    pthread_mutex_unlock(&queue->mutex);

    return DC_SUCCESS;
}

void message_queue_destroy(message_queue_t *queue) {
    if (!queue) return;

    pthread_mutex_destroy(&queue->mutex);
    pthread_cond_destroy(&queue->not_empty);
    pthread_cond_destroy(&queue->not_full);

    free(queue->messages);
    free(queue);
}

// 消息处理实现
dc_error_code_t dc_message_create(dc_message_t **msg, uint16_t cmd, const void *data, uint32_t data_len) {
    if (!msg) return DC_ERROR_INVALID_PARAM;

    dc_message_t *new_msg = calloc(1, sizeof(dc_message_t));
    if (!new_msg) return DC_ERROR_MEMORY_ALLOC;

    new_msg->cmd = cmd;
    new_msg->len = sizeof(dc_message_t) - sizeof(uint8_t*) + data_len;
    new_msg->timestamp = (uint32_t)time(NULL);
    new_msg->data_len = data_len;

    if (data && data_len > 0) {
        new_msg->data = malloc(data_len);
        if (!new_msg->data) {
            free(new_msg);
            return DC_ERROR_MEMORY_ALLOC;
        }
        memcpy(new_msg->data, data, data_len);
    }

    // 计算校验和
    new_msg->checksum = calculate_checksum(new_msg, sizeof(dc_message_t) - sizeof(uint8_t*));
    if (new_msg->data) {
        new_msg->checksum ^= calculate_checksum(new_msg->data, data_len);
    }

    *msg = new_msg;
    return DC_SUCCESS;
}

void dc_message_destroy(dc_message_t *msg) {
    if (!msg) return;

    if (msg->data) {
        free(msg->data);
    }
    free(msg);
}

dc_error_code_t dc_message_validate(const dc_message_t *msg) {
    if (!msg) return DC_ERROR_INVALID_PARAM;

    // 验证校验和
    uint8_t calculated_checksum = calculate_checksum(msg, sizeof(dc_message_t) - sizeof(uint8_t*));
    if (msg->data) {
        calculated_checksum ^= calculate_checksum(msg->data, msg->data_len);
    }

    if (calculated_checksum != msg->checksum) {
        return DC_ERROR_CHECKSUM;
    }

    return DC_SUCCESS;
}

// 设备管理实现
dc_error_code_t dc_device_set_status(dc_device_manager_t *mgr, uint32_t device_id, device_status_t status) {
    if (!mgr || !is_valid_device_id(device_id)) {
        return DC_ERROR_INVALID_PARAM;
    }

    pthread_mutex_lock(&mgr->devices[device_id].mutex);
    mgr->devices[device_id].status = status;
    pthread_mutex_unlock(&mgr->devices[device_id].mutex);

    return DC_SUCCESS;
}

device_status_t dc_device_get_status(dc_device_manager_t *mgr, uint32_t device_id) {
    if (!mgr || !is_valid_device_id(device_id)) {
        return DEVICE_STATUS_INVALID;
    }

    pthread_mutex_lock(&mgr->devices[device_id].mutex);
    device_status_t status = mgr->devices[device_id].status;
    pthread_mutex_unlock(&mgr->devices[device_id].mutex);

    return status;
}

bool dc_device_is_online(dc_device_manager_t *mgr, uint32_t device_id) {
    if (!mgr || !is_valid_device_id(device_id)) {
        return false;
    }

    pthread_mutex_lock(&mgr->devices[device_id].mutex);
    bool online = mgr->devices[device_id].is_online;
    pthread_mutex_unlock(&mgr->devices[device_id].mutex);

    return online;
}

// 记录控制实现
dc_error_code_t dc_record_start(dc_device_manager_t *mgr, const dc_record_param_t *param) {
    if (!mgr || !param) return DC_ERROR_INVALID_PARAM;

    // 验证时间范围
    if (param->mode == RECORD_MODE_TIMED) {
        dc_error_code_t time_check = validate_time_range(param->start_time_ms, param->end_time_ms);
        if (time_check != DC_SUCCESS) return time_check;
    }

    pthread_mutex_lock(&mgr->param_mutex);

    // 检查设备状态
    for (int i = 0; i < REC_MD_NUM; i++) {
        if (param->channel_mask[i] != 0) {
            if (!dc_device_is_online(mgr, i)) {
                pthread_mutex_unlock(&mgr->param_mutex);
                return DC_ERROR_DEVICE_OFFLINE;
            }

            device_status_t status = dc_device_get_status(mgr, i);
            if (status != DEVICE_STATUS_IDLE) {
                pthread_mutex_unlock(&mgr->param_mutex);
                return DC_ERROR_DEVICE_BUSY;
            }
        }
    }

    // 更新记录参数
    memcpy(&mgr->record_param, param, sizeof(dc_record_param_t));

    // 启动记录
    for (int i = 0; i < REC_MD_NUM; i++) {
        if (param->channel_mask[i] != 0) {
            dc_device_set_status(mgr, i, DEVICE_STATUS_RECORDING);
        }
    }

    pthread_mutex_unlock(&mgr->param_mutex);
    return DC_SUCCESS;
}

dc_error_code_t dc_record_stop(dc_device_manager_t *mgr, uint32_t device_mask) {
    if (!mgr) return DC_ERROR_INVALID_PARAM;

    for (int i = 0; i < REC_MD_NUM; i++) {
        if (device_mask & (1 << i)) {
            device_status_t status = dc_device_get_status(mgr, i);
            if (status == DEVICE_STATUS_RECORDING) {
                dc_device_set_status(mgr, i, DEVICE_STATUS_IDLE);
            }
        }
    }

    return DC_SUCCESS;
}

// 通信管理器实现
dc_communication_manager_t* dc_comm_manager_create(void) {
    dc_communication_manager_t *mgr = calloc(1, sizeof(dc_communication_manager_t));
    if (!mgr) return NULL;

    // 初始化互斥锁
    if (pthread_mutex_init(&mgr->global_mutex, NULL) != 0) {
        free(mgr);
        return NULL;
    }

    return mgr;
}

dc_error_code_t dc_comm_manager_init(dc_communication_manager_t *mgr) {
    if (!mgr) return DC_ERROR_INVALID_PARAM;

    // 创建设备管理器
    mgr->device_mgr = calloc(1, sizeof(dc_device_manager_t));
    if (!mgr->device_mgr) return DC_ERROR_MEMORY_ALLOC;

    // 初始化设备信息
    for (int i = 0; i < REC_MD_NUM; i++) {
        mgr->device_mgr->devices[i].device_id = i;
        mgr->device_mgr->devices[i].status = DEVICE_STATUS_IDLE;
        mgr->device_mgr->devices[i].is_online = true;
        pthread_mutex_init(&mgr->device_mgr->devices[i].mutex, NULL);
    }

    pthread_mutex_init(&mgr->device_mgr->param_mutex, NULL);

    // 创建内存池
    mgr->mem_pool = memory_pool_create(DC_MAX_MSG_SIZE, 100);
    if (!mgr->mem_pool) return DC_ERROR_MEMORY_ALLOC;

    // 创建消息队列
    mgr->msg_queue = message_queue_create(1000);
    if (!mgr->msg_queue) return DC_ERROR_MEMORY_ALLOC;

    // 创建统计信息
    mgr->stats = calloc(1, sizeof(dc_statistics_t));
    if (!mgr->stats) return DC_ERROR_MEMORY_ALLOC;
    pthread_mutex_init(&mgr->stats->mutex, NULL);

    mgr->is_initialized = true;
    return DC_SUCCESS;
}

void dc_comm_manager_destroy(dc_communication_manager_t *mgr) {
    if (!mgr) return;

    if (mgr->device_mgr) {
        for (int i = 0; i < REC_MD_NUM; i++) {
            pthread_mutex_destroy(&mgr->device_mgr->devices[i].mutex);
        }
        pthread_mutex_destroy(&mgr->device_mgr->param_mutex);
        free(mgr->device_mgr);
    }

    if (mgr->mem_pool) {
        memory_pool_destroy(mgr->mem_pool);
    }

    if (mgr->msg_queue) {
        message_queue_destroy(mgr->msg_queue);
    }

    if (mgr->stats) {
        pthread_mutex_destroy(&mgr->stats->mutex);
        free(mgr->stats);
    }

    pthread_mutex_destroy(&mgr->global_mutex);
    free(mgr);
}
