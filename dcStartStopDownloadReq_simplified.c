// 原始版本的dcStartStopDownloadReq函数
int dcStartStopDownloadReq_original(T_DcCommunicationReqList* pReqList)
{
    DC_COMMUNICATION_REQ *pReq;
    uint8_x checkSum;
    int nRecordDeviceTag;
    int ret;
    DC_COMMUNICATION_RSP DcRsp;
    int busy = 0;
    uint32_x workStatus;
    
    pReq = (DC_COMMUNICATION_REQ*)pReqList->pBuf;
    switchDcCommonReqBodyEndian(pReq);
    checkSum = calcCheckSum((uint8_x*)&pReq->commonHead.len, sizeof(DC_COMMUNICATION_REQ)-DC_HEAD_FLAG_LEN-1); 
    if(checkSum != pReq->checkSum)
    {
        CRP_APP_ERR("cmd %d invalid checkSum:%#x, should be %#x", pReq->commonHead.cmd, checkSum, pReq->checkSum);
        ret = -1;
    }
    else
    {
        if(getDcCtrlDbgStatus())
        {
            CRP_APP_INFO("%s(%#x): deviceIdOfFile:%#x visitFilePath:%s", getCmdDesc(pReq->commonHead.cmd), pReq->commonHead.cmd, pReq->deviceIdOfFile, pReq->visitFilePath);
        }
        nRecordDeviceTag = pReq->deviceIdOfFile - 1;
        if(nRecordDeviceTag == 0 || nRecordDeviceTag == 1)
        {
            if(getRecLiveStatus(nRecordDeviceTag))
            {
                workStatus = getRecWorkStatus(nRecordDeviceTag);
                if(pReq->commonHead.cmd == DC_CMD_START_DOWNLOAD) /* 开始转存 */
                {
                    if(workStatus == IDLE_STATUS || workStatus == DOWNLOAD_STATUS)
                    {
                        ret = clearAutoDownloadIdleCount(nRecordDeviceTag);
                        if(ret == 0)
                        {
                            ret = recDownloadControlOneFileReq(nRecordDeviceTag, SCF_START, (char*)pReq->visitFilePath, STD_MMOUNT_DIR);
                        }
                    }
                    else
                    {
                        CRP_APP_ERR("nRecordDeviceTag:%d WorkStatus:%d, busy!", nRecordDeviceTag, workStatus);
                        ret = -1;
                        busy = 1;
                    }
                }
                else if(pReq->commonHead.cmd == DC_CMD_STOP_DOWNLOAD) /* 停止转存 */
                {
                    if(workStatus == IDLE_STATUS || workStatus == DOWNLOAD_STATUS)
                    {
                        ret = recDownloadControlOneFileReq(nRecordDeviceTag, SCF_STOP, NULL, NULL);
                    }
                    else
                    {
                        CRP_APP_ERR("nRecordDeviceTag:%d WorkStatus:%d, busy!", nRecordDeviceTag, workStatus);
                        ret = -1;
                        busy = 1;
                    }
                }
                else
                {
                    ret = -1;
                }
            }
            else
            {
                CRP_APP_ERR("nRecordDeviceTag:%d LiveStatus:%d", nRecordDeviceTag, getRecLiveStatus(nRecordDeviceTag));
                ret = -1;
            }
        }
        else
        {
            CRP_APP_ERR("invalid deviceIdOfFile:%#x", nRecordDeviceTag);
            ret = -1;
        }
    }

    fillDcRspCommunicateFrame(&DcRsp, &pReq->commonHead, (ret==0)?0:((busy)?DC_ERROR_NO_BUSY:-1), NULL, 0);
    sendDcCommunicateRsp(&DcRsp);
    return ret;
}

// ============================================================================
// 简化版本 - 使用早期返回和辅助函数
// ============================================================================

// 辅助函数：验证设备ID
static bool is_valid_device_id(int device_id) {
    return (device_id == 0 || device_id == 1);
}

// 辅助函数：检查设备状态是否允许下载操作
static bool is_download_allowed(uint32_x work_status) {
    return (work_status == IDLE_STATUS || work_status == DOWNLOAD_STATUS);
}

// 辅助函数：处理开始下载命令
static int handle_start_download(int device_tag, const char* file_path) {
    int ret = clearAutoDownloadIdleCount(device_tag);
    if (ret != 0) {
        return ret;
    }
    
    return recDownloadControlOneFileReq(device_tag, SCF_START, (char*)file_path, STD_MMOUNT_DIR);
}

// 辅助函数：处理停止下载命令
static int handle_stop_download(int device_tag) {
    return recDownloadControlOneFileReq(device_tag, SCF_STOP, NULL, NULL);
}

// 辅助函数：处理设备忙碌状态
static int handle_device_busy(int device_tag, uint32_x work_status, int* busy_flag) {
    CRP_APP_ERR("nRecordDeviceTag:%d WorkStatus:%d, busy!", device_tag, work_status);
    *busy_flag = 1;
    return -1;
}

// 简化后的主函数
int dcStartStopDownloadReq_simplified(T_DcCommunicationReqList* pReqList)
{
    DC_COMMUNICATION_REQ *pReq;
    DC_COMMUNICATION_RSP DcRsp;
    uint8_x checkSum;
    int nRecordDeviceTag;
    int ret = -1;
    int busy = 0;
    uint32_x workStatus;
    
    // 参数验证
    if (!pReqList) {
        CRP_APP_ERR("Invalid parameter: pReqList is NULL");
        goto send_response;
    }
    
    pReq = (DC_COMMUNICATION_REQ*)pReqList->pBuf;
    switchDcCommonReqBodyEndian(pReq);
    
    // 校验和检查
    checkSum = calcCheckSum((uint8_x*)&pReq->commonHead.len, 
                           sizeof(DC_COMMUNICATION_REQ) - DC_HEAD_FLAG_LEN - 1);
    if (checkSum != pReq->checkSum) {
        CRP_APP_ERR("cmd %d invalid checkSum:%#x, should be %#x", 
                   pReq->commonHead.cmd, checkSum, pReq->checkSum);
        goto send_response;
    }
    
    // 调试信息输出
    if (getDcCtrlDbgStatus()) {
        CRP_APP_INFO("%s(%#x): deviceIdOfFile:%#x visitFilePath:%s", 
                    getCmdDesc(pReq->commonHead.cmd), pReq->commonHead.cmd, 
                    pReq->deviceIdOfFile, pReq->visitFilePath);
    }
    
    // 设备ID验证
    nRecordDeviceTag = pReq->deviceIdOfFile - 1;
    if (!is_valid_device_id(nRecordDeviceTag)) {
        CRP_APP_ERR("invalid deviceIdOfFile:%#x", nRecordDeviceTag);
        goto send_response;
    }
    
    // 设备在线状态检查
    if (!getRecLiveStatus(nRecordDeviceTag)) {
        CRP_APP_ERR("nRecordDeviceTag:%d LiveStatus:%d", 
                   nRecordDeviceTag, getRecLiveStatus(nRecordDeviceTag));
        goto send_response;
    }
    
    // 获取设备工作状态
    workStatus = getRecWorkStatus(nRecordDeviceTag);
    
    // 根据命令类型处理
    switch (pReq->commonHead.cmd) {
        case DC_CMD_START_DOWNLOAD:
            if (is_download_allowed(workStatus)) {
                ret = handle_start_download(nRecordDeviceTag, (char*)pReq->visitFilePath);
            } else {
                ret = handle_device_busy(nRecordDeviceTag, workStatus, &busy);
            }
            break;
            
        case DC_CMD_STOP_DOWNLOAD:
            if (is_download_allowed(workStatus)) {
                ret = handle_stop_download(nRecordDeviceTag);
            } else {
                ret = handle_device_busy(nRecordDeviceTag, workStatus, &busy);
            }
            break;
            
        default:
            CRP_APP_ERR("Unsupported command: %#x", pReq->commonHead.cmd);
            ret = -1;
            break;
    }

send_response:
    // 统一的响应处理
    fillDcRspCommunicateFrame(&DcRsp, &pReq->commonHead, 
                             (ret == 0) ? 0 : (busy ? DC_ERROR_NO_BUSY : -1), 
                             NULL, 0);
    sendDcCommunicateRsp(&DcRsp);
    return ret;
}

// ============================================================================
// 进一步优化版本 - 使用函数指针和状态机模式
// ============================================================================

// 命令处理函数类型定义
typedef int (*download_cmd_handler_t)(int device_tag, const char* file_path);

// 命令处理函数映射
typedef struct {
    uint16_t cmd_id;
    download_cmd_handler_t handler;
    const char* cmd_name;
} download_cmd_mapping_t;

// 开始下载处理函数
static int start_download_handler(int device_tag, const char* file_path) {
    int ret = clearAutoDownloadIdleCount(device_tag);
    return (ret == 0) ? recDownloadControlOneFileReq(device_tag, SCF_START, 
                                                     (char*)file_path, STD_MMOUNT_DIR) : ret;
}

// 停止下载处理函数
static int stop_download_handler(int device_tag, const char* file_path) {
    (void)file_path;  // 停止命令不需要文件路径
    return recDownloadControlOneFileReq(device_tag, SCF_STOP, NULL, NULL);
}

// 命令映射表
static const download_cmd_mapping_t cmd_mappings[] = {
    {DC_CMD_START_DOWNLOAD, start_download_handler, "开始转储"},
    {DC_CMD_STOP_DOWNLOAD,  stop_download_handler,  "停止转储"}
};

// 查找命令处理函数
static download_cmd_handler_t find_command_handler(uint16_t cmd_id) {
    for (size_t i = 0; i < sizeof(cmd_mappings) / sizeof(cmd_mappings[0]); i++) {
        if (cmd_mappings[i].cmd_id == cmd_id) {
            return cmd_mappings[i].handler;
        }
    }
    return NULL;
}

// 最优化版本
int dcStartStopDownloadReq_optimized(T_DcCommunicationReqList* pReqList)
{
    DC_COMMUNICATION_REQ *pReq = (DC_COMMUNICATION_REQ*)pReqList->pBuf;
    DC_COMMUNICATION_RSP DcRsp;
    int ret = -1;
    int busy = 0;
    
    // 早期验证 - 使用宏简化重复代码
    #define VALIDATE_AND_RETURN_IF_FAILED(condition, error_msg, ...) \
        if (!(condition)) { \
            CRP_APP_ERR(error_msg, ##__VA_ARGS__); \
            goto send_response; \
        }
    
    VALIDATE_AND_RETURN_IF_FAILED(pReqList, "Invalid parameter: pReqList is NULL");
    
    switchDcCommonReqBodyEndian(pReq);
    
    // 校验和验证
    uint8_x checkSum = calcCheckSum((uint8_x*)&pReq->commonHead.len, 
                                   sizeof(DC_COMMUNICATION_REQ) - DC_HEAD_FLAG_LEN - 1);
    VALIDATE_AND_RETURN_IF_FAILED(checkSum == pReq->checkSum, 
                                  "cmd %d invalid checkSum:%#x, should be %#x", 
                                  pReq->commonHead.cmd, checkSum, pReq->checkSum);
    
    // 调试信息
    if (getDcCtrlDbgStatus()) {
        CRP_APP_INFO("%s(%#x): deviceIdOfFile:%#x visitFilePath:%s", 
                    getCmdDesc(pReq->commonHead.cmd), pReq->commonHead.cmd, 
                    pReq->deviceIdOfFile, pReq->visitFilePath);
    }
    
    // 设备验证
    int nRecordDeviceTag = pReq->deviceIdOfFile - 1;
    VALIDATE_AND_RETURN_IF_FAILED(is_valid_device_id(nRecordDeviceTag), 
                                  "invalid deviceIdOfFile:%#x", nRecordDeviceTag);
    VALIDATE_AND_RETURN_IF_FAILED(getRecLiveStatus(nRecordDeviceTag), 
                                  "nRecordDeviceTag:%d LiveStatus:%d", 
                                  nRecordDeviceTag, getRecLiveStatus(nRecordDeviceTag));
    
    // 命令处理
    download_cmd_handler_t handler = find_command_handler(pReq->commonHead.cmd);
    VALIDATE_AND_RETURN_IF_FAILED(handler, "Unsupported command: %#x", pReq->commonHead.cmd);
    
    uint32_x workStatus = getRecWorkStatus(nRecordDeviceTag);
    if (is_download_allowed(workStatus)) {
        ret = handler(nRecordDeviceTag, (char*)pReq->visitFilePath);
    } else {
        ret = handle_device_busy(nRecordDeviceTag, workStatus, &busy);
    }
    
    #undef VALIDATE_AND_RETURN_IF_FAILED

send_response:
    fillDcRspCommunicateFrame(&DcRsp, &pReq->commonHead, 
                             (ret == 0) ? 0 : (busy ? DC_ERROR_NO_BUSY : -1), 
                             NULL, 0);
    sendDcCommunicateRsp(&DcRsp);
    return ret;
}
